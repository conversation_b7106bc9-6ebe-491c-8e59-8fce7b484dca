Option Explicit

' =============================================================================
' 工具函数模块 - 希尔顿酒店管理系统
' 提供系统测试、验证功能和通用工具方法
' 依赖：ModGlobal.bas (PWD常量)
' =============================================================================

' =============================================================================
' 通用工具方法 - 从BusinessForecastManager提取
' =============================================================================

' 工作表保护/解除保护通用操作
Public Sub UnprotectWorksheet(ws As Worksheet, ByRef wasProtected As Boolean)
    On Error Resume Next
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If
End Sub

Public Sub ProtectWorksheet(ws As Worksheet, wasProtected As Boolean)
    On Error Resume Next
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
End Sub

' 标记单元格为未完成状态
Public Sub MarkCellAsIncomplete(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    With ws.Range(cellAddress)
        .Borders.LineStyle = xlContinuous
        .Borders.Color = RGB(255, 0, 0)  ' 红色边框
        .Borders.Weight = xlMedium
        .Interior.Color = RGB(255, 230, 230)  ' 浅红色背景
    End With

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "标记单元格失败: " & cellAddress & " - " & Err.Description, ws.Name, "格式标记")
End Sub

' 清除验证标记并恢复正常表格边框
Public Sub ClearValidationMarks(ws As Worksheet, dataRange As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ' 清除红色验证标记背景色
    ws.Range(dataRange).Interior.ColorIndex = xlNone

    ' 恢复标准表格边框
    Call SetStandardTableBorders(ws, dataRange)

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除验证标记失败: " & Err.Description, ws.Name, "格式清除")
End Sub

' 设置标准表格边框
Public Sub SetStandardTableBorders(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress).Borders
        .LineStyle = xlContinuous
        .Color = RGB(166, 166, 166)  ' A6A6A6颜色
        .Weight = xlThin
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置表格边框失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 清除数据区域通用方法
Public Sub ClearDataRange(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ws.Range(rangeAddress).ClearContents

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除数据区域失败: " & rangeAddress & " - " & Err.Description, ws.Name, "数据清除")
End Sub

' 设置警告信息
Public Sub SetWarningMessage(ws As Worksheet, cellAddress As String, message As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    With ws.Range(cellAddress)
        .Value = message
        .Font.Color = RGB(255, 0, 0)  ' 红色字体
    End With

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "设置警告信息失败: " & cellAddress & " - " & Err.Description, ws.Name, "警告设置")
End Sub

' 清除警告信息
Public Sub ClearWarningMessage(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ws.Range(cellAddress).ClearContents

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除警告信息失败: " & cellAddress & " - " & Err.Description, ws.Name, "警告清除")
End Sub

' 设置标准数据单元格格式（白色背景、微软雅黑、10号字体）
Public Sub SetStandardDataCellFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress)
        .Interior.Color = RGB(255, 255, 255)  ' 白色背景
        .Font.Name = "微软雅黑"               ' 微软雅黑字体
        .Font.Size = 10                       ' 10号字体
        .Font.Bold = False                    ' 非粗体
        .HorizontalAlignment = xlCenter       ' 水平居中
        .VerticalAlignment = xlCenter         ' 垂直居中
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置标准数据单元格格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 统一光标处理方法 - 确保Add More功能的用户体验一致性
Public Sub ClearCursorSelection()
    On Error Resume Next

    ' 方法1: 尝试取消选择状态
    Application.CutCopyMode = False

    ' 方法2: 将光标移动到A1单元格（非数据区域）
    Dim activeWs As Worksheet
    Set activeWs = ActiveSheet
    If Not activeWs Is Nothing Then
        '不要选择A1单元格，选择B列的最后一行单元格
        activeWs.Range("B1000").End(xlUp).Select 
    End If

    ' 方法3: 清除剪贴板状态
    Application.CutCopyMode = False

    Call LogManager.LogEvent("DEBUG", "光标选择状态已清除", activeWs.Name, "光标处理")
End Sub

' 状态栏管理操作
Public Sub SetStatusBar(message As String)
    On Error Resume Next
    Application.StatusBar = message
End Sub

Public Sub ClearStatusBar()
    On Error Resume Next
    Application.StatusBar = False
End Sub


Option Explicit

' =============================================================================
' 工具函数模块 - 希尔顿酒店管理系统
' 提供系统测试、验证功能和通用工具方法
' 依赖：ModGlobal.bas (PWD常量)
' =============================================================================

' =============================================================================
' 样式常量定义 - 统一管理所有样式参数
' =============================================================================

' 区域标题行样式常量（"年度重大事件"、"生意板块管理"文字所在行）
Public Const REGION_TITLE_FONT_NAME As String = "宋体"
Public Const REGION_TITLE_FONT_SIZE As Integer = 16
Public Const REGION_TITLE_BACKGROUND_COLOR As Long = &HD9D9D9  ' #D9D9D9浅灰色
Public Const REGION_TITLE_FONT_COLOR As Long = &H97114D       ' #114D97深蓝色
Public Const REGION_TITLE_FONT_BOLD As Boolean = False

' 副标题行样式常量（区域标题行的下一行）
Public Const SUB_TITLE_FONT_NAME As String = "微软雅黑"
Public Const SUB_TITLE_FONT_SIZE As Integer = 9
Public Const SUB_TITLE_BACKGROUND_COLOR As Long = &H97114D    ' #114D97深蓝色
Public Const SUB_TITLE_FONT_COLOR As Long = &HFFFFFF         ' 白色
Public Const SUB_TITLE_FONT_BOLD As Boolean = False

' 标准数据单元格样式常量
Public Const DATA_FONT_NAME As String = "微软雅黑"
Public Const DATA_FONT_SIZE As Integer = 10
Public Const DATA_BACKGROUND_COLOR As Long = &HFFFFFF  ' 白色
Public Const DATA_FONT_COLOR As Long = &H0             ' 黑色
Public Const DATA_FONT_BOLD As Boolean = False

' 边框样式常量
Public Const BORDER_COLOR As Long = &HA6A6A6           ' #A6A6A6灰色
Public Const BORDER_WEIGHT As Integer = xlThin
Public Const BORDER_STYLE As Integer = xlContinuous

' 对齐方式常量
Public Const ALIGN_CENTER As Integer = xlCenter
Public Const ALIGN_LEFT As Integer = xlLeft
Public Const ALIGN_V_CENTER As Integer = xlCenter

' 警告和错误样式常量
Public Const WARNING_FONT_COLOR As Long = &H0000FF     ' 红色
Public Const ERROR_BACKGROUND_COLOR As Long = &HE6C8C8 ' 浅红色背景
Public Const ERROR_FONT_COLOR As Long = &H0000FF       ' 红色字体
Public Const ERROR_BORDER_COLOR As Long = &H0000FF     ' 红色边框

' =============================================================================
' 通用工具方法 - 从BusinessForecastManager提取
' =============================================================================

' 工作表保护/解除保护通用操作
Public Sub UnprotectWorksheet(ws As Worksheet, ByRef wasProtected As Boolean)
    On Error Resume Next
    wasProtected = ws.ProtectContents
    If wasProtected Then
        ws.Unprotect Password:=PWD
    End If
End Sub

Public Sub ProtectWorksheet(ws As Worksheet, wasProtected As Boolean)
    On Error Resume Next
    If wasProtected Then
        ws.Protect Password:=PWD, DrawingObjects:=True, Contents:=True, Scenarios:=True, _
                   AllowFormattingCells:=True, AllowFormattingColumns:=True, AllowFormattingRows:=True
    End If
End Sub

' 标记单元格为未完成状态
Public Sub MarkCellAsIncomplete(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    With ws.Range(cellAddress)
        .Borders.LineStyle = xlContinuous
        .Borders.Color = ERROR_BORDER_COLOR
        .Borders.Weight = xlMedium
        .Interior.Color = ERROR_BACKGROUND_COLOR
    End With

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "标记单元格失败: " & cellAddress & " - " & Err.Description, ws.Name, "格式标记")
End Sub

' 清除验证标记并恢复标准数据格式
Public Sub ClearValidationMarks(ws As Worksheet, dataRange As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ' 恢复完整的标准数据格式（包含样式和边框）
    Call SetCompleteDataCellFormat(ws, dataRange)

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除验证标记失败: " & Err.Description, ws.Name, "格式清除")
End Sub

' 设置标准表格边框
Public Sub SetStandardTableBorders(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress).Borders
        .LineStyle = BORDER_STYLE
        .Color = BORDER_COLOR
        .Weight = BORDER_WEIGHT
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置表格边框失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 设置完整的区域标题行格式（无边框）
Public Sub SetCompleteRegionTitleRowFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ' 设置区域标题行样式（已包含无边框设置）
    Call SetRegionTitleRowFormat(ws, rangeAddress)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置完整区域标题行格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 设置完整的副标题行格式（包含边框）
Public Sub SetCompleteSubTitleRowFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ' 设置副标题行样式
    Call SetSubTitleRowFormat(ws, rangeAddress)

    ' 设置边框
    Call SetStandardTableBorders(ws, rangeAddress)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置完整副标题行格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 设置完整的数据单元格格式（包含边框）
Public Sub SetCompleteDataCellFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ' 设置数据单元格样式
    Call SetStandardDataCellFormat(ws, rangeAddress)

    ' 设置边框
    Call SetStandardTableBorders(ws, rangeAddress)

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置完整数据单元格格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 清除数据区域通用方法
Public Sub ClearDataRange(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ws.Range(rangeAddress).ClearContents

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除数据区域失败: " & rangeAddress & " - " & Err.Description, ws.Name, "数据清除")
End Sub

' 设置警告信息
Public Sub SetWarningMessage(ws As Worksheet, cellAddress As String, message As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    With ws.Range(cellAddress)
        .Value = message
        .Font.Color = WARNING_FONT_COLOR
    End With

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "设置警告信息失败: " & cellAddress & " - " & Err.Description, ws.Name, "警告设置")
End Sub

' 清除警告信息
Public Sub ClearWarningMessage(ws As Worksheet, cellAddress As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call UnprotectWorksheet(ws, wasProtected)

    ws.Range(cellAddress).ClearContents

    Call ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "清除警告信息失败: " & cellAddress & " - " & Err.Description, ws.Name, "警告清除")
End Sub

' 设置标准数据单元格格式（白色背景、微软雅黑、10号字体）
Public Sub SetStandardDataCellFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress)
        .Interior.Color = DATA_BACKGROUND_COLOR
        .Font.Name = DATA_FONT_NAME
        .Font.Size = DATA_FONT_SIZE
        .Font.Color = DATA_FONT_COLOR
        .Font.Bold = DATA_FONT_BOLD
        .HorizontalAlignment = ALIGN_CENTER
        .VerticalAlignment = ALIGN_CENTER
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置标准数据单元格格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 设置区域标题行格式（浅灰色背景、深蓝色字体、宋体、16号字体）
Public Sub SetRegionTitleRowFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress)
        .Interior.Color = REGION_TITLE_BACKGROUND_COLOR
        .Font.Name = REGION_TITLE_FONT_NAME
        .Font.Size = REGION_TITLE_FONT_SIZE
        .Font.Color = REGION_TITLE_FONT_COLOR
        .Font.Bold = REGION_TITLE_FONT_BOLD
        .HorizontalAlignment = ALIGN_LEFT
        .VerticalAlignment = ALIGN_V_CENTER
        ' 区域标题行无边框
        .Borders.LineStyle = xlNone
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置区域标题行格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 设置副标题行格式（深蓝色背景、白色字体、微软雅黑、9号字体）
Public Sub SetSubTitleRowFormat(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    With ws.Range(rangeAddress)
        .Interior.Color = SUB_TITLE_BACKGROUND_COLOR
        .Font.Name = SUB_TITLE_FONT_NAME
        .Font.Size = SUB_TITLE_FONT_SIZE
        .Font.Color = SUB_TITLE_FONT_COLOR
        .Font.Bold = SUB_TITLE_FONT_BOLD
        .HorizontalAlignment = ALIGN_CENTER
        .VerticalAlignment = ALIGN_CENTER
    End With

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "设置副标题行格式失败: " & rangeAddress & " - " & Err.Description, ws.Name, "格式设置")
End Sub

' 统一光标处理方法 - 确保Add More功能的用户体验一致性
Public Sub ClearCursorSelection()
    On Error Resume Next

    ' 方法1: 尝试取消选择状态
    Application.CutCopyMode = False

    ' 方法2: 将光标移动到A1单元格（非数据区域）
    Dim activeWs As Worksheet
    Set activeWs = ActiveSheet
    If Not activeWs Is Nothing Then
        '不要选择A1单元格，选择B列的最后一行单元格
        activeWs.Range("B1000").End(xlUp).Select 
    End If

    ' 方法3: 清除剪贴板状态
    Application.CutCopyMode = False

    Call LogManager.LogEvent("DEBUG", "光标选择状态已清除", activeWs.Name, "光标处理")
End Sub

' 状态栏管理操作
Public Sub SetStatusBar(message As String)
    On Error Resume Next
    Application.StatusBar = message
End Sub

Public Sub ClearStatusBar()
    On Error Resume Next
    Application.StatusBar = False
End Sub


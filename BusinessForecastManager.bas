Option Explicit

' =============================================================================
' 业务背景预测管理模块 - 希尔顿酒店管理系统
' 实现"2. 业务背景"工作表中酒店生意预测功能
' 支持13个版本的数据管理：2025年预算（只读）+ RF1-RF12（可编辑）
' =============================================================================

' 工作表名称常量
Private Const BUSINESS_SHEET As String = "2. 业务背景"

' 单元格地址常量
Private Const VERSION_CELL As String = "O4"        ' 版本选择下拉列表
Private Const DATA_START_ROW As Integer = 6        ' 数据开始行
Private Const DATA_END_ROW As Integer = 8          ' 数据结束行
Private Const DATA_START_COL As Integer = 3        ' 数据开始列（C列）
Private Const DATA_END_COL As Integer = 14         ' 数据结束列（N列）

' 版本常量
Private Const VERSION_ANNUAL As String = "2025年预算"
Private Const VERSION_PREFIX As String = "RF"

' 类别常量
Private Const CATEGORY_OCC As String = "OCC"
Private Const CATEGORY_ADR As String = "ADR"
Private Const CATEGORY_REVPAR As String = "RevPAR"

' 酒店生意细分占比区域常量
Private Const SEGMENT_START_ROW As Integer = 12       ' 细分占比开始行
Private Const SEGMENT_END_ROW As Integer = 24         ' 细分占比结束行
Private Const SEGMENT_DATA_COL As Integer = 3         ' 数据列（C列）
Private Const SEGMENT_WARNING_CELL As String = "D10"  ' 警告单元格

' 酒店渠道占比区域常量
Private Const CHANNEL_START_ROW As Integer = 13       ' 渠道占比开始行
Private Const CHANNEL_END_ROW As Integer = 17         ' 渠道占比结束行
Private Const CHANNEL_DATA_COL As Integer = 15        ' 数据列（O列）
Private Const CHANNEL_WARNING_CELL As String = "N10"  ' 警告单元格

' Hotel GOB Analysis区域常量
Private Const GOB_START_ROW As Integer = 29           ' GOB分析开始行
Private Const GOB_END_ROW As Integer = 38             ' GOB分析结束行
Private Const GOB_DATA_COL As Integer = 3             ' 数据列（C列）
Private Const GOB_WARNING_CELL As String = "B40"      ' 警告单元格

' 统一动态表格区域常量
Private Const UNIFIED_START_ROW As Integer = 41        ' 统一起始行（标题行）
Private Const UNIFIED_START_COL As Integer = 2         ' B列
Private Const UNIFIED_END_COL As Integer = 12          ' L列
Private Const UNIFIED_MAX_SCAN_ROW As Integer = 100    ' 最大扫描行数

' 区域标识常量
Private Const PRICE_MARKER As String = "价格管理"
Private Const EVENTS_MARKER As String = "年度重大事件"
Private Const SEGMENT_MARKER As String = "生意板块管理"

' 生意板块管理动态表格常量
Private Const SEGMENT_DYNAMIC_START_COL As Integer = 3     ' C列
Private Const SEGMENT_DYNAMIC_END_COL As Integer = 5       ' E列
Private Const SEGMENT_DYNAMIC_MAX_ROWS As Integer = 10     ' 最大行数限制
Private Const SEGMENT_ADD_MORE_TEXT As String = "' + add more"
Private Const SEGMENT_ADD_MORE_BUTTON As String = "Add_More_Segment"

' =============================================================================
' 主要入口函数
' =============================================================================

' 初始化业务背景工作表
Public Sub InitializeBusinessForecastSheet()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "初始化")
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 设置版本下拉列表
    Dim versionList As String
    versionList = VERSION_ANNUAL & ",RF1,RF2,RF3,RF4,RF5,RF6,RF7,RF8,RF9,RF10,RF11,RF12"

    With ws.Range(VERSION_CELL).Validation
        .Delete
        .Add Type:=xlValidateList, AlertStyle:=xlValidAlertStop, _
             Formula1:=versionList
        .IgnoreBlank = True
        .InCellDropdown = True
        .ShowInput = True
        .ShowError = True
    End With

    ' 设置默认版本
    If Trim(CStr(ws.Range(VERSION_CELL).Value)) = "" Then
        ws.Range(VERSION_CELL).Value = VERSION_ANNUAL
    End If

    ' 设置固定标签
    ws.Range("B6").Value = CATEGORY_OCC
    ws.Range("B7").Value = CATEGORY_ADR
    ws.Range("B8").Value = CATEGORY_REVPAR

    ' 设置酒店生意细分占比区域标签
    Call InitializeSegmentRatioArea(ws)

    ' 设置酒店渠道占比区域标签
    Call InitializeChannelRatioArea(ws)

    ' 设置Hotel GOB Analysis区域标签
    Call InitializeHotelGOBArea(ws)

    ' 初始化所有Add More功能（年度重大事件 + 生意板块管理）
    Call SetupAllAddMoreCells

    ' 设置月份表头
    Dim monthNames As Variant
    monthNames = Array("1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月")

    Dim col As Integer
    For col = 0 To 11
        ws.Cells(5, DATA_START_COL + col).Value = monthNames(col)
    Next col

    ' 设置数据区域的标准表格边框
    Call Utils.SetStandardTableBorders(ws, "C6:N8")
    Call Utils.SetStandardTableBorders(ws, "C12:C24")
    Call Utils.SetStandardTableBorders(ws, "O13:O17")
    Call Utils.SetStandardTableBorders(ws, "C29:C38")

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("INFO", "业务背景工作表初始化完成", BUSINESS_SHEET, "初始化")
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "业务背景工作表初始化失败: " & Err.Description, BUSINESS_SHEET, "初始化")
End Sub

' 处理版本选择变化事件
Public Sub HandleVersionChange(ws As Worksheet, newVersion As String)
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在切换预测版本...")
    Call LogManager.StartNewOperationSession("版本切换", BUSINESS_SHEET)

    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) = "" Then
        Call LogManager.LogEvent("ERROR", "无法获取当前用户Inn Code", BUSINESS_SHEET, "权限验证")
        Call Utils.ClearStatusBar
        Exit Sub
    End If

    ' 清除当前数据区域并加载新版本数据
    Call Utils.ClearDataRange(ws, "C6:N8")
    Call LoadForecastData(ws, currentInnCode, newVersion)
    Call SetReadOnlyStatus(ws, newVersion)

    ' 如果是RF版本，清除可能存在的验证标记
    If newVersion <> VERSION_ANNUAL Then Call Utils.ClearValidationMarks(ws, "C6:N8")

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("版本切换完成: " & newVersion)
    Call LogManager.LogEvent("INFO", "版本切换完成: " & newVersion, BUSINESS_SHEET, "版本切换")
    Call LogManager.EndCurrentOperationSession("版本切换", "SUCCESS", BUSINESS_SHEET)
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("版本切换失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "版本切换失败: " & Err.Description, BUSINESS_SHEET, "版本切换")
    Call LogManager.EndCurrentOperationSession("版本切换", "FAILED", BUSINESS_SHEET)
End Sub

' 保存预测数据到数据库
Public Sub SaveForecastDataToDB()
    On Error GoTo ErrorHandler
    
    Call Utils.SetStatusBar("正在保存预测数据...")
    
    Call LogManager.StartNewOperationSession("保存预测数据", BUSINESS_SHEET)
    
    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    
    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "工作表验证")
        Call Utils.ClearStatusBar
        Exit Sub
    End If

    ' 获取当前版本和Inn Code
    Dim currentVersion As String, currentInnCode As String
    currentVersion = Trim(CStr(DataHelper.GetCellValue(ws, VERSION_CELL)))
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If currentVersion = "" Or currentInnCode = "" Then
        Call LogManager.LogEvent("ERROR", "版本或Inn Code为空", BUSINESS_SHEET, "数据验证")
        Call Utils.ClearStatusBar
        Exit Sub
    End If

    ' 检查是否为已提交的年度预算
    If currentVersion = VERSION_ANNUAL And IsVersionSubmitted(currentInnCode, currentVersion) Then
        Call DataHelper.ShowSyncStatus("年度预算已提交，无法修改")
        Call Utils.ClearStatusBar
        Exit Sub
    End If

    ' 2025年预算版本的特殊处理（静默模式）
    If currentVersion = VERSION_ANNUAL Then
        ' 数据完整性检查
        Dim isDataComplete As Boolean
        isDataComplete = ValidateForecastData(ws)

        ' 静默处理：记录数据完整性状态并继续提交
        If isDataComplete Then
            Call LogManager.LogEvent("INFO", "2025年预算数据完整性检查通过，准备提交", BUSINESS_SHEET, "数据验证")
            Call Utils.SetStatusBar("2025年预算数据完整，正在提交...")
        Else
            Call LogManager.LogEvent("WARNING", "2025年预算数据不完整但继续提交", BUSINESS_SHEET, "数据验证")
            Call Utils.SetStatusBar("2025年预算数据不完整，正在提交...")
        End If

        ' 清除验证标记
        Call Utils.ClearValidationMarks(ws, "C6:N8")
        Call LogManager.LogEvent("INFO", "自动提交2025年预算，数据完整性: " & IIf(isDataComplete, "完整", "不完整"), BUSINESS_SHEET, "自动操作")
    End If
    
    ' 保存数据
    If SaveForecastDataToDatabase(ws, currentInnCode, currentVersion) Then
        ' 如果是年度预算，立即标记为已提交并设置只读
        If currentVersion = VERSION_ANNUAL Then
            Call MarkVersionAsSubmitted(currentInnCode, currentVersion)
            Call SetReadOnlyStatus(ws, currentVersion)
            Call Utils.ClearStatusBar
            Call DataHelper.ShowSyncStatus("2025年预算提交成功，已设置为只读状态")
            Call LogManager.LogEvent("INFO", "2025年预算提交成功，已标记为已提交状态", BUSINESS_SHEET, "数据保存")
        Else
            Call Utils.ClearStatusBar
            Call DataHelper.ShowSyncStatus("预测数据保存成功")
            Call LogManager.LogEvent("INFO", "预测数据保存成功: " & currentVersion, BUSINESS_SHEET, "数据保存")
        End If
        Call LogManager.EndCurrentOperationSession("保存预测数据", "SUCCESS", BUSINESS_SHEET)
    Else
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("预测数据保存失败")
        Call LogManager.EndCurrentOperationSession("保存预测数据", "FAILED", BUSINESS_SHEET)
    End If
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("保存预测数据异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "保存预测数据异常: " & Err.Description, BUSINESS_SHEET, "数据保存")
    Call LogManager.EndCurrentOperationSession("保存预测数据", "FAILED", BUSINESS_SHEET)
End Sub

' 从数据库加载预测数据
Public Sub LoadForecastDataFromDB()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在加载预测数据...")
    Call LogManager.StartNewOperationSession("加载预测数据", BUSINESS_SHEET)

    Dim ws As Worksheet, currentVersion As String, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentVersion = Trim(CStr(DataHelper.GetCellValue(ws, VERSION_CELL)))
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Then
        Call Utils.ClearStatusBar
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "工作表验证")
        Exit Sub
    End If

    If currentVersion = "" Or currentInnCode = "" Then
        Call LogManager.LogEvent("WARNING", "版本或Inn Code为空，使用默认版本", BUSINESS_SHEET, "数据验证")
        currentVersion = VERSION_ANNUAL
    End If

    ' 加载数据并设置只读状态
    Call LoadForecastData(ws, currentInnCode, currentVersion)
    Call SetReadOnlyStatus(ws, currentVersion)

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("预测数据加载完成")
    Call LogManager.LogEvent("INFO", "预测数据加载完成", BUSINESS_SHEET, "数据加载")
    Call LogManager.EndCurrentOperationSession("加载预测数据", "SUCCESS", BUSINESS_SHEET)
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载预测数据异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "加载预测数据异常: " & Err.Description, BUSINESS_SHEET, "数据加载")
    Call LogManager.EndCurrentOperationSession("加载预测数据", "FAILED", BUSINESS_SHEET)
End Sub

' 手动验证当前版本的数据完整性
Public Sub ValidateCurrentVersionData()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet, currentVersion As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentVersion = Trim(CStr(DataHelper.GetCellValue(ws, VERSION_CELL)))

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "工作表验证")
        Call Utils.SetStatusBar("工作表验证失败")
        Exit Sub
    End If

    If currentVersion = "" Then
        Call DataHelper.ShowSyncStatus("请先选择版本")
        Exit Sub
    End If

    ' 执行数据验证
    Call Utils.SetStatusBar("正在验证数据完整性...")
    Dim isComplete As Boolean
    isComplete = ValidateForecastData(ws)

    Call DataHelper.ShowSyncStatus(IIf(isComplete, "数据完整性检查通过 - 所有单元格均已填写", "发现未填写的单元格，已用红色标记"))
    Call LogManager.LogEvent("INFO", "手动数据验证完成，版本: " & currentVersion & "，结果: " & IIf(isComplete, "完整", "不完整"), BUSINESS_SHEET, "手动验证")
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("数据验证异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "手动数据验证异常: " & Err.Description, BUSINESS_SHEET, "手动验证")
End Sub

' 清除当前的验证标记
Public Sub ClearCurrentValidationMarks()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "工作表验证")
        Exit Sub
    End If

    Call Utils.ClearValidationMarks(ws, "C6:N8")
    Call DataHelper.ShowSyncStatus("已清除验证标记")
    Call LogManager.LogEvent("INFO", "手动清除验证标记", BUSINESS_SHEET, "手动操作")
    Exit Sub

ErrorHandler:
    Call DataHelper.ShowSyncStatus("清除验证标记失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "清除验证标记异常: " & Err.Description, BUSINESS_SHEET, "手动操作")
End Sub

' =============================================================================
' 酒店生意细分占比区域功能 - 公共入口方法
' =============================================================================

' 手动保存细分占比数据到数据库
Public Sub SaveSegmentRatioToDB()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在保存细分占比数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", "无法获取用户权限"))
        Exit Sub
    End If

    ' 验证数据并检查是否有有效数据
    Call ValidateRatioSum(ws, SEGMENT_START_ROW, SEGMENT_END_ROW, SEGMENT_DATA_COL, SEGMENT_WARNING_CELL, "细分占比")

    Dim hasData As Boolean, row As Integer
    For row = SEGMENT_START_ROW To SEGMENT_END_ROW
        If IsNumeric(ws.Cells(row, SEGMENT_DATA_COL).Value) And ws.Cells(row, SEGMENT_DATA_COL).Value <> "" Then
            hasData = True: Exit For
        End If
    Next row

    If Not hasData Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("无有效数据需要保存")
        Exit Sub
    End If

    ' 确保数据表存在并保存数据
    Call Utils.SetStatusBar("正在保存到数据库...")
    Call EnsureDataTable("business_segment_ratio", "unique_segment")

    ' 保存数据
    Dim successCount As Integer, cellValue As Variant, sql As String, recordId As String
    For row = SEGMENT_START_ROW To SEGMENT_END_ROW
        cellValue = ws.Cells(row, SEGMENT_DATA_COL).Value

        If IsNumeric(cellValue) And cellValue <> "" Then
            recordId = "BSR-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
            sql = "INSERT INTO business_segment_ratio (id, inn_code, row_index, ratio_value) VALUES ('" & _
                  recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & row & ", " & cellValue & _
                  ") ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
        Else
            sql = "DELETE FROM business_segment_ratio WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & row
        End If

        If DabaseCore.ExecuteCommandSilent(sql) Then successCount = successCount + 1
    Next row

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus(IIf(successCount > 0, "细分占比数据保存成功", "细分占比数据保存失败"))
    If successCount > 0 Then Call LogManager.LogEvent("INFO", "保存成功: " & successCount & " 条记录", BUSINESS_SHEET, "数据保存")
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("保存异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "保存异常: " & Err.Description, BUSINESS_SHEET, "数据保存")
End Sub

' 从数据库恢复细分占比数据
Public Sub LoadSegmentRatioFromDB()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在加载细分占比数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Or Not DabaseCore.TestConnection() Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", IIf(Trim(currentInnCode) = "", "无法获取用户权限", "数据库连接失败")))
        Exit Sub
    End If

    ' 清除当前数据
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    Call Utils.ClearDataRange(ws, "C12:C24")
    Call Utils.ClearWarningMessage(ws, SEGMENT_WARNING_CELL)

    ' 从数据库加载数据
    Call Utils.SetStatusBar("正在从数据库加载数据...")

    Dim sql As String, rs As Object, loadedCount As Integer
    sql = "SELECT row_index, ratio_value FROM business_segment_ratio WHERE inn_code = '" & _
          DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    If Not rs Is Nothing Then
        Do While Not rs.EOF
            Dim rowIndex As Integer, ratioValue As Double
            rowIndex = CInt(rs.Fields("row_index").Value)
            ratioValue = CDbl(rs.Fields("ratio_value").Value)

            If rowIndex >= SEGMENT_START_ROW And rowIndex <= SEGMENT_END_ROW Then
                ws.Cells(rowIndex, SEGMENT_DATA_COL).Value = ratioValue
                loadedCount = loadedCount + 1
            End If
            rs.MoveNext
        Loop
        rs.Close: Set rs = Nothing
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call ValidateRatioSum(ws, SEGMENT_START_ROW, SEGMENT_END_ROW, SEGMENT_DATA_COL, SEGMENT_WARNING_CELL, "细分占比")

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载完成，共 " & loadedCount & " 条记录")
    Call LogManager.LogEvent("INFO", "加载完成: " & loadedCount & " 条记录", BUSINESS_SHEET, "数据加载")
    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "加载异常: " & Err.Description, BUSINESS_SHEET, "数据加载")
End Sub

' 处理酒店生意细分占比区域数据变化（自动触发）
Public Sub HandleSegmentRatioChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 检查是否在细分占比数据区域
    If changedRange.Row >= SEGMENT_START_ROW And changedRange.Row <= SEGMENT_END_ROW And changedRange.Column = SEGMENT_DATA_COL Then
        ' 验证总和并更新警告
        Call ValidateRatioSum(ws, SEGMENT_START_ROW, SEGMENT_END_ROW, SEGMENT_DATA_COL, SEGMENT_WARNING_CELL, "细分占比")

        ' 静默保存到数据库
        Dim currentInnCode As String
        currentInnCode = TokenManager.GetCurrentHotelInncode()

        If Trim(currentInnCode) <> "" Then
            Dim cellValue As Variant
            cellValue = changedRange.Value

            Dim sql As String
            If IsNumeric(cellValue) And cellValue <> "" Then
                Dim recordId As String
                recordId = "BSR-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
                sql = "INSERT INTO business_segment_ratio (id, inn_code, row_index, ratio_value) " & _
                      "VALUES ('" & recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & changedRange.Row & ", " & cellValue & ") " & _
                      "ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
            Else
                sql = "DELETE FROM business_segment_ratio WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & changedRange.Row
            End If
            Call DabaseCore.ExecuteCommandSilent(sql)
        End If
    End If

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "处理细分占比变化异常: " & Err.Description, BUSINESS_SHEET, "数据变化")
End Sub

' =============================================================================
' 酒店渠道占比区域功能 - 公共入口方法
' =============================================================================

' 手动保存渠道占比数据到数据库
Public Sub SaveChannelRatioToDB()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在保存渠道占比数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", "无法获取用户权限"))
        Exit Sub
    End If

    ' 验证数据并检查是否有有效数据
    Call ValidateRatioSum(ws, CHANNEL_START_ROW, CHANNEL_END_ROW, CHANNEL_DATA_COL, CHANNEL_WARNING_CELL, "渠道占比")

    Dim hasData As Boolean, row As Integer
    For row = CHANNEL_START_ROW To CHANNEL_END_ROW
        If IsNumeric(ws.Cells(row, CHANNEL_DATA_COL).Value) And ws.Cells(row, CHANNEL_DATA_COL).Value <> "" Then
            hasData = True: Exit For
        End If
    Next row

    If Not hasData Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("无有效数据需要保存")
        Exit Sub
    End If

    ' 确保数据表存在并保存数据
    Call Utils.SetStatusBar("正在保存到数据库...")
    Call EnsureDataTable("hotel_channel_ratio", "unique_channel")

    ' 保存数据
    Dim successCount As Integer, cellValue As Variant, sql As String, recordId As String
    For row = CHANNEL_START_ROW To CHANNEL_END_ROW
        cellValue = ws.Cells(row, CHANNEL_DATA_COL).Value

        If IsNumeric(cellValue) And cellValue <> "" Then
            recordId = "HCR-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
            sql = "INSERT INTO hotel_channel_ratio (id, inn_code, row_index, ratio_value) VALUES ('" & _
                  recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & row & ", " & cellValue & _
                  ") ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
        Else
            sql = "DELETE FROM hotel_channel_ratio WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & row
        End If

        If DabaseCore.ExecuteCommandSilent(sql) Then successCount = successCount + 1
    Next row

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus(IIf(successCount > 0, "渠道占比数据保存成功", "渠道占比数据保存失败"))
    If successCount > 0 Then Call LogManager.LogEvent("INFO", "保存成功: " & successCount & " 条记录", BUSINESS_SHEET, "数据保存")
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("保存异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "保存异常: " & Err.Description, BUSINESS_SHEET, "数据保存")
End Sub

' 从数据库恢复渠道占比数据
Public Sub LoadChannelRatioFromDB()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在加载渠道占比数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Or Not DabaseCore.TestConnection() Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", IIf(Trim(currentInnCode) = "", "无法获取用户权限", "数据库连接失败")))
        Exit Sub
    End If

    ' 清除当前数据
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    Call Utils.ClearDataRange(ws, "O13:O17")
    Call Utils.ClearWarningMessage(ws, CHANNEL_WARNING_CELL)

    ' 从数据库加载数据
    Call Utils.SetStatusBar("正在从数据库加载数据...")

    Dim sql As String, rs As Object, loadedCount As Integer
    sql = "SELECT row_index, ratio_value FROM hotel_channel_ratio WHERE inn_code = '" & _
          DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    If Not rs Is Nothing Then
        Do While Not rs.EOF
            Dim rowIndex As Integer, ratioValue As Double
            rowIndex = CInt(rs.Fields("row_index").Value)
            ratioValue = CDbl(rs.Fields("ratio_value").Value)

            If rowIndex >= CHANNEL_START_ROW And rowIndex <= CHANNEL_END_ROW Then
                ws.Cells(rowIndex, CHANNEL_DATA_COL).Value = ratioValue
                loadedCount = loadedCount + 1
            End If
            rs.MoveNext
        Loop
        rs.Close: Set rs = Nothing
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call ValidateRatioSum(ws, CHANNEL_START_ROW, CHANNEL_END_ROW, CHANNEL_DATA_COL, CHANNEL_WARNING_CELL, "渠道占比")

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载完成，共 " & loadedCount & " 条记录")
    Call LogManager.LogEvent("INFO", "加载完成: " & loadedCount & " 条记录", BUSINESS_SHEET, "数据加载")
    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "加载异常: " & Err.Description, BUSINESS_SHEET, "数据加载")
End Sub

' 处理酒店渠道占比区域数据变化（自动触发）
Public Sub HandleChannelRatioChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 检查是否在渠道占比数据区域
    If changedRange.Row >= CHANNEL_START_ROW And changedRange.Row <= CHANNEL_END_ROW And changedRange.Column = CHANNEL_DATA_COL Then
        ' 验证总和并更新警告
        Call ValidateRatioSum(ws, CHANNEL_START_ROW, CHANNEL_END_ROW, CHANNEL_DATA_COL, CHANNEL_WARNING_CELL, "渠道占比")

        ' 静默保存到数据库
        Dim currentInnCode As String
        currentInnCode = TokenManager.GetCurrentHotelInncode()

        If Trim(currentInnCode) <> "" Then
            Dim cellValue As Variant
            cellValue = changedRange.Value

            Dim sql As String
            If IsNumeric(cellValue) And cellValue <> "" Then
                Dim recordId As String
                recordId = "HCR-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
                sql = "INSERT INTO hotel_channel_ratio (id, inn_code, row_index, ratio_value) " & _
                      "VALUES ('" & recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & changedRange.Row & ", " & cellValue & ") " & _
                      "ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
            Else
                sql = "DELETE FROM hotel_channel_ratio WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & changedRange.Row
            End If
            Call DabaseCore.ExecuteCommandSilent(sql)
        End If
    End If

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "处理渠道占比变化异常: " & Err.Description, BUSINESS_SHEET, "数据变化")
End Sub

' =============================================================================
' Hotel GOB Analysis区域功能 - 公共入口方法
' =============================================================================

' 手动保存Hotel GOB Analysis数据到数据库
Public Sub SaveHotelGOBData()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在保存Hotel GOB Analysis数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", "无法获取用户权限"))
        Exit Sub
    End If

    ' 验证数据并检查是否有有效数据
    Call ValidateRatioSum(ws, GOB_START_ROW, GOB_END_ROW, GOB_DATA_COL, GOB_WARNING_CELL, "Hotel GOB Analysis")

    Dim hasData As Boolean, row As Integer
    For row = GOB_START_ROW To GOB_END_ROW
        If IsNumeric(ws.Cells(row, GOB_DATA_COL).Value) And ws.Cells(row, GOB_DATA_COL).Value <> "" Then
            hasData = True: Exit For
        End If
    Next row

    If Not hasData Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("无有效数据需要保存")
        Exit Sub
    End If

    ' 确保数据表存在并保存数据
    Call Utils.SetStatusBar("正在保存到数据库...")

    ' 保存数据
    Dim successCount As Integer, cellValue As Variant, sql As String, recordId As String
    For row = GOB_START_ROW To GOB_END_ROW
        cellValue = ws.Cells(row, GOB_DATA_COL).Value

        If IsNumeric(cellValue) And cellValue <> "" Then
            recordId = "HGB-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
            sql = "INSERT INTO hotel_gob_analysis (id, inn_code, row_index, ratio_value) VALUES ('" & _
                  recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & row & ", " & cellValue & _
                  ") ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
        Else
            sql = "DELETE FROM hotel_gob_analysis WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & row
        End If

        If DabaseCore.ExecuteCommandSilent(sql) Then successCount = successCount + 1
    Next row

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus(IIf(successCount > 0, "Hotel GOB Analysis数据保存成功", "Hotel GOB Analysis数据保存失败"))
    If successCount > 0 Then Call LogManager.LogEvent("INFO", "保存成功: " & successCount & " 条记录", BUSINESS_SHEET, "数据保存")
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("保存异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "保存异常: " & Err.Description, BUSINESS_SHEET, "数据保存")
End Sub

' 从数据库恢复Hotel GOB Analysis数据
Public Sub LoadHotelGOBData()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在加载Hotel GOB Analysis数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Or Not DabaseCore.TestConnection() Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", IIf(Trim(currentInnCode) = "", "无法获取用户权限", "数据库连接失败")))
        Exit Sub
    End If

    ' 清除当前数据
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    Call Utils.ClearDataRange(ws, "C29:C38")
    Call Utils.ClearWarningMessage(ws, GOB_WARNING_CELL)

    ' 从数据库加载数据
    Call Utils.SetStatusBar("正在从数据库加载数据...")

    Dim sql As String, rs As Object, loadedCount As Integer
    sql = "SELECT row_index, ratio_value FROM hotel_gob_analysis WHERE inn_code = '" & _
          DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    If Not rs Is Nothing Then
        Do While Not rs.EOF
            Dim rowIndex As Integer, ratioValue As Double
            rowIndex = CInt(rs.Fields("row_index").Value)
            ratioValue = CDbl(rs.Fields("ratio_value").Value)

            If rowIndex >= GOB_START_ROW And rowIndex <= GOB_END_ROW Then
                ws.Cells(rowIndex, GOB_DATA_COL).Value = ratioValue
                loadedCount = loadedCount + 1
            End If
            rs.MoveNext
        Loop
        rs.Close: Set rs = Nothing
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call ValidateRatioSum(ws, GOB_START_ROW, GOB_END_ROW, GOB_DATA_COL, GOB_WARNING_CELL, "Hotel GOB Analysis")

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载完成，共 " & loadedCount & " 条记录")
    Call LogManager.LogEvent("INFO", "加载完成: " & loadedCount & " 条记录", BUSINESS_SHEET, "数据加载")
    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载异常: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "加载异常: " & Err.Description, BUSINESS_SHEET, "数据加载")
End Sub

' 处理Hotel GOB Analysis区域数据变化（自动触发）
Public Sub HandleHotelGOBChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 检查是否在Hotel GOB Analysis数据区域
    If changedRange.Row >= GOB_START_ROW And changedRange.Row <= GOB_END_ROW And changedRange.Column = GOB_DATA_COL Then
        ' 验证总和并更新警告
        Call ValidateRatioSum(ws, GOB_START_ROW, GOB_END_ROW, GOB_DATA_COL, GOB_WARNING_CELL, "Hotel GOB Analysis")

        ' 静默保存到数据库
        Dim currentInnCode As String
        currentInnCode = TokenManager.GetCurrentHotelInncode()

        If Trim(currentInnCode) <> "" Then
            Dim cellValue As Variant
            cellValue = changedRange.Value

            Dim sql As String
            If IsNumeric(cellValue) And cellValue <> "" Then
                Dim recordId As String
                recordId = "HGB-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
                sql = "INSERT INTO hotel_gob_analysis (id, inn_code, row_index, ratio_value) " & _
                      "VALUES ('" & recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & changedRange.Row & ", " & cellValue & ") " & _
                      "ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
            Else
                sql = "DELETE FROM hotel_gob_analysis WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' AND row_index = " & changedRange.Row
            End If
            Call DabaseCore.ExecuteCommandSilent(sql)
        End If
    End If

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "处理Hotel GOB Analysis变化异常: " & Err.Description, BUSINESS_SHEET, "数据变化")
End Sub

' 处理个人旅游价区域数据变化（自动触发）
Public Sub HandlePersonalTravelPriceChange(ws As Worksheet, changedRange As Range)
    On Error GoTo ErrorHandler

    ' 个人旅游价区域通常在动态表格区域，这里添加基本的处理逻辑
    ' 检查是否在个人旅游价相关区域（可根据实际需求调整范围）
    If changedRange.Row >= UNIFIED_START_ROW And changedRange.Row <= UNIFIED_MAX_SCAN_ROW Then
        ' 静默保存到数据库
        Dim currentInnCode As String
        currentInnCode = TokenManager.GetCurrentHotelInncode()

        If Trim(currentInnCode) <> "" Then
            Call EnsureDataTable("personal_travel_price", "unique_travel_price")

            Dim cellValue As Variant, sql As String, recordId As String
            cellValue = changedRange.Value

            If cellValue <> "" Then
                recordId = "PTP-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
                sql = "INSERT INTO personal_travel_price (id, inn_code, row_index, column_index, cell_value) VALUES ('" & _
                      recordId & "', '" & DataHelper.EscapeSQL(currentInnCode) & "', " & changedRange.Row & ", " & changedRange.Column & ", '" & _
                      DataHelper.EscapeSQL(CStr(cellValue)) & "') ON DUPLICATE KEY UPDATE cell_value = '" & _
                      DataHelper.EscapeSQL(CStr(cellValue)) & "', updated_at = NOW()"
            Else
                sql = "DELETE FROM personal_travel_price WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & _
                      "' AND row_index = " & changedRange.Row & " AND column_index = " & changedRange.Column
            End If

            Call DabaseCore.ExecuteCommandSilent(sql)
            Call LogManager.LogEvent("DEBUG", "个人旅游价数据变化已保存: " & changedRange.Address, BUSINESS_SHEET, "数据变化")
        End If
    End If

    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "处理个人旅游价变化异常: " & Err.Description, BUSINESS_SHEET, "数据变化")
End Sub

' 初始化酒店生意细分占比区域
Private Sub InitializeSegmentRatioArea(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 设置B12-B24区域的标签为"BAR"
    Dim row As Integer
    For row = SEGMENT_START_ROW To SEGMENT_END_ROW
        ws.Cells(row, 2).Value = "BAR"
    Next row

    ' 加载现有数据
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) <> "" And DabaseCore.TestConnection() Then
        Dim sql As String
        sql = "SELECT row_index, ratio_value FROM business_segment_ratio " & _
              "WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"

        Dim rs As Object
        Set rs = DabaseCore.ExecuteQuerySilent(sql)

        If Not rs Is Nothing Then
            Do While Not rs.EOF
                Dim rowIndex As Integer
                Dim ratioValue As Double
                rowIndex = CInt(rs.Fields("row_index").Value)
                ratioValue = CDbl(rs.Fields("ratio_value").Value)

                If rowIndex >= SEGMENT_START_ROW And rowIndex <= SEGMENT_END_ROW Then
                    ws.Cells(rowIndex, SEGMENT_DATA_COL).Value = ratioValue
                End If
                rs.MoveNext
            Loop
            rs.Close
            Set rs = Nothing
        End If
    End If

    ' 验证总和
    Call ValidateRatioSum(ws, SEGMENT_START_ROW, SEGMENT_END_ROW, SEGMENT_DATA_COL, SEGMENT_WARNING_CELL, "细分占比")

    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    Call LogManager.LogEvent("ERROR", "初始化细分占比区域异常: " & Err.Description, BUSINESS_SHEET, "初始化")
End Sub

' 通用数据表创建函数
Private Sub EnsureDataTable(tableName As String, uniqueKeyName As String)
    On Error Resume Next
    If Not DabaseCore.TableExists(tableName) Then
        Dim createSQL As String
        If tableName = "unified_dynamic_region" Then
            createSQL = "CREATE TABLE " & tableName & " (" & _
                       "id VARCHAR(50) PRIMARY KEY," & _
                       "inn_code VARCHAR(50) NOT NULL," & _
                       "row_index INT NOT NULL," & _
                       "column_index INT NOT NULL," & _
                       "value TEXT," & _
                       "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," & _
                       "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," & _
                       "UNIQUE KEY " & uniqueKeyName & " (inn_code, row_index, column_index)" & _
                       ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        Else
            createSQL = "CREATE TABLE " & tableName & " (" & _
                       "id VARCHAR(50) PRIMARY KEY," & _
                       "inn_code VARCHAR(50) NOT NULL," & _
                       "row_index INT NOT NULL," & _
                       "ratio_value DECIMAL(5,4) NOT NULL," & _
                       "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP," & _
                       "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP," & _
                       "UNIQUE KEY " & uniqueKeyName & " (inn_code, row_index)" & _
                       ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        End If
        Call DabaseCore.ExecuteCommandSilent(createSQL)
    End If
End Sub



' 初始化酒店渠道占比区域
Private Sub InitializeChannelRatioArea(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 设置N13-N17区域的标签
    Dim channelLabels As Variant
    channelLabels = Array("Property Direct", "OTA", "Fliggy", "DD", "GDS")

    Dim row As Integer
    For row = 0 To 4
        ws.Cells(CHANNEL_START_ROW + row, CHANNEL_DATA_COL - 1).Value = channelLabels(row)
    Next row

    ' 加载现有数据
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) <> "" And DabaseCore.TestConnection() Then
        Dim sql As String
        sql = "SELECT row_index, ratio_value FROM hotel_channel_ratio " & _
              "WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"

        Dim rs As Object
        Set rs = DabaseCore.ExecuteQuerySilent(sql)

        If Not rs Is Nothing Then
            Do While Not rs.EOF
                Dim rowIndex As Integer
                Dim ratioValue As Double
                rowIndex = CInt(rs.Fields("row_index").Value)
                ratioValue = CDbl(rs.Fields("ratio_value").Value)

                If rowIndex >= CHANNEL_START_ROW And rowIndex <= CHANNEL_END_ROW Then
                    ws.Cells(rowIndex, CHANNEL_DATA_COL).Value = ratioValue
                End If
                rs.MoveNext
            Loop
            rs.Close
            Set rs = Nothing
        End If
    End If

    ' 验证总和
    Call ValidateRatioSum(ws, CHANNEL_START_ROW, CHANNEL_END_ROW, CHANNEL_DATA_COL, CHANNEL_WARNING_CELL, "渠道占比")

    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call LogManager.LogEvent("ERROR", "初始化渠道占比区域异常: " & Err.Description, BUSINESS_SHEET, "初始化")
End Sub





' =============================================================================
' 统一动态表格区域处理 - 新方案
' =============================================================================

' 保存统一动态区域数据到数据库（性能优化版）
Public Sub SaveUnifiedDynamicRegionData()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在保存动态区域数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", "无法获取用户权限"))
        Exit Sub
    End If

    ' 确保数据表存在并获取数据范围
    Call EnsureDataTable("unified_dynamic_region", "unique_cell")
    Dim lastDataRow As Integer
    lastDataRow = DetectUnifiedRegionLastRow(ws)

    If lastDataRow < UNIFIED_START_ROW Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("没有找到动态区域数据")
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 批量读取数据并处理合并单元格
    Dim dataArray As Variant
    dataArray = ws.Range("B" & UNIFIED_START_ROW & ":L" & lastDataRow).Value

    ' 处理合并单元格：将合并单元格的值复制到所有相关位置
    Call ProcessMergedCellsInArray(ws, dataArray, UNIFIED_START_ROW, UNIFIED_START_COL, lastDataRow, UNIFIED_END_COL)

    ' 构建批量插入SQL
    Dim sqlValues As String, valueCount As Integer
    Dim row As Integer, col As Integer, cellValue As Variant

    For row = 1 To UBound(dataArray, 1)
        For col = 1 To UBound(dataArray, 2)
            cellValue = dataArray(row, col)
            If Not IsEmpty(cellValue) And Trim(CStr(cellValue)) <> "" Then
                If sqlValues <> "" Then sqlValues = sqlValues & ","
                sqlValues = sqlValues & "('" & "UDR" & Format(Now, "yyyymmddhhnnss") & Right("0000" & Int(Rnd() * 10000), 4) & _
                           "','" & DataHelper.EscapeSQL(currentInnCode) & "'," & _
                           (UNIFIED_START_ROW + row - 1) & "," & (UNIFIED_START_COL + col - 1) & _
                           ",'" & DataHelper.EscapeSQL(CStr(cellValue)) & "')"
                valueCount = valueCount + 1
            End If
        Next col
    Next row

    ' 执行批量数据库操作
    If valueCount > 0 Then
        Dim deleteSQL As String, insertSQL As String
        deleteSQL = "DELETE FROM unified_dynamic_region WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "'"
        insertSQL = "INSERT INTO unified_dynamic_region (id, inn_code, row_index, column_index, value) VALUES " & sqlValues

        If DabaseCore.ExecuteCommandSilent(deleteSQL) And DabaseCore.ExecuteCommandSilent(insertSQL) Then
            Call DataHelper.ShowSyncStatus("保存成功，共 " & valueCount & " 项")
        Else
            Call DataHelper.ShowSyncStatus("保存失败")
        End If
    Else
        Call DataHelper.ShowSyncStatus("无数据需要保存")
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("保存异常: " & Err.Description)
End Sub

' 从数据库恢复统一动态区域数据（性能优化版）
Public Sub LoadUnifiedDynamicRegionData()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在加载动态区域数据...")

    Dim ws As Worksheet, currentInnCode As String
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If ws Is Nothing Or Trim(currentInnCode) = "" Or Not DabaseCore.TestConnection() Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus(IIf(ws Is Nothing, "找不到工作表", IIf(Trim(currentInnCode) = "", "无法获取用户权限", "数据库连接失败")))
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 清空现有动态区域并初始化
    Call ClearUnifiedDynamicRegion(ws)

    ' 从数据库批量加载数据
    Dim sql As String, rs As Object
    sql = "SELECT row_index, column_index, value FROM unified_dynamic_region " & _
          "WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' " & _
          "ORDER BY row_index, column_index"
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    Dim loadedCount As Integer, maxRow As Integer, maxCol As Integer
    maxRow = UNIFIED_START_ROW: maxCol = UNIFIED_START_COL

    If Not rs Is Nothing Then
        ' 确定数据范围
        Do While Not rs.EOF
            Dim rowIndex As Integer, colIndex As Integer
            rowIndex = CInt(rs.Fields("row_index").Value)
            colIndex = CInt(rs.Fields("column_index").Value)
            If rowIndex > maxRow Then maxRow = rowIndex
            If colIndex > maxCol Then maxCol = colIndex
            rs.MoveNext
        Loop
        rs.MoveFirst

        ' 创建数组并填充数据
        If maxRow >= UNIFIED_START_ROW And maxCol >= UNIFIED_START_COL Then
            Dim dataArray As Variant
            ReDim dataArray(1 To maxRow - UNIFIED_START_ROW + 1, 1 To maxCol - UNIFIED_START_COL + 1)

            Do While Not rs.EOF
                Dim cellValue As String
                rowIndex = CInt(rs.Fields("row_index").Value)
                colIndex = CInt(rs.Fields("column_index").Value)
                cellValue = CStr(rs.Fields("value").Value)

                dataArray(rowIndex - UNIFIED_START_ROW + 1, colIndex - UNIFIED_START_COL + 1) = cellValue
                loadedCount = loadedCount + 1
                rs.MoveNext
            Loop

            ' 一次性写入Excel
            ws.Range(ws.Cells(UNIFIED_START_ROW, UNIFIED_START_COL), ws.Cells(maxRow, maxCol)).Value = dataArray
        End If

        rs.Close: Set rs = Nothing

        ' 应用格式和重建合并单元格
        Call ApplyUnifiedRegionFormatting(ws)
        Call RebuildAllRegionMergedCells(ws)
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus(IIf(loadedCount > 0, "加载成功，共 " & loadedCount & " 项", "无数据"))
    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("加载异常: " & Err.Description)
End Sub



' =============================================================================
' 生意板块管理动态表格功能
' =============================================================================

' 检测生意板块管理区域的位置
' 位置：第1182-1198行
' 功能：在B列中查找"生意板块管理"标记文本的确切位置
Private Function DetectSegmentManagementRegion(ws As Worksheet) As Integer
    On Error Resume Next

    Dim checkRow As Integer
    DetectSegmentManagementRegion = 0

    ' 调试输出：开始检测
    Call LogManager.LogEvent("DEBUG", "开始检测生意板块管理区域位置", BUSINESS_SHEET, "位置检测")
    Call LogManager.LogEvent("DEBUG", "扫描范围: B" & UNIFIED_START_ROW & " 到 B" & UNIFIED_MAX_SCAN_ROW, BUSINESS_SHEET, "位置检测")
    Call LogManager.LogEvent("DEBUG", "查找标记: '" & SEGMENT_MARKER & "'", BUSINESS_SHEET, "位置检测")

    ' 从统一动态区域开始扫描，查找"生意板块管理"标记
    For checkRow = UNIFIED_START_ROW To UNIFIED_MAX_SCAN_ROW
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 2).Value))

        ' 调试输出：扫描过程
        If cellValue <> "" Then
            Call LogManager.LogEvent("DEBUG", "B" & checkRow & " 内容: '" & cellValue & "'", BUSINESS_SHEET, "位置检测")
        End If

        If cellValue = SEGMENT_MARKER Then
            DetectSegmentManagementRegion = checkRow
            Call LogManager.LogEvent("INFO", "✅ 找到生意板块管理标记，位置: B" & checkRow, BUSINESS_SHEET, "位置检测")
            Exit For
        End If
    Next checkRow

    ' 调试输出：检测结果
    If DetectSegmentManagementRegion = 0 Then
        Call LogManager.LogEvent("WARNING", "❌ 未找到生意板块管理标记", BUSINESS_SHEET, "位置检测")
    End If
End Function



' 检测年度重大事件区域的位置
Private Function DetectEventsManagementRegion(ws As Worksheet) As Integer
    On Error Resume Next

    Dim checkRow As Integer
    DetectEventsManagementRegion = 0

    ' 扫描查找"年度重大事件"标记
    For checkRow = UNIFIED_START_ROW To UNIFIED_MAX_SCAN_ROW
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 2).Value))

        If cellValue = EVENTS_MARKER Then
            DetectEventsManagementRegion = checkRow
            Call LogManager.LogEvent("INFO", "找到年度重大事件标记，位置: B" & checkRow, BUSINESS_SHEET, "位置检测")
            Exit For
        End If
    Next checkRow
End Function

' 设置Add More文字（简化版 - 基于E列文本）
Public Sub SetupAllAddMoreCells()
    On Error GoTo ErrorHandler

    Call LogManager.LogEvent("INFO", "开始设置Add More文字", BUSINESS_SHEET, "Add More设置")

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call LogManager.LogEvent("ERROR", "找不到工作表: " & BUSINESS_SHEET, BUSINESS_SHEET, "Add More设置")
        Exit Sub
    End If

    ' 保存事件状态并禁用事件，防止触发SegmentForm
    Dim originalEventsState As Boolean
    originalEventsState = Application.EnableEvents
    Application.EnableEvents = False

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 1. 清理所有现有Add More内容
    Call CleanupAllAddMoreContent(ws)

    ' 2. 设置年度重大事件Add More文字
    Call SetupEventsAddMoreText(ws)

    ' 3. 设置生意板块管理Add More文字
    Call SetupSegmentAddMoreText(ws)

    Call Utils.ProtectWorksheet(ws, wasProtected)

    ' 恢复事件状态
    Application.EnableEvents = originalEventsState

    Call LogManager.LogEvent("INFO", "Add More文字设置完成", BUSINESS_SHEET, "Add More设置")
    Exit Sub

ErrorHandler:
    ' 确保在错误情况下也恢复事件状态
    Application.EnableEvents = originalEventsState
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "设置Add More文字失败: " & Err.Description, BUSINESS_SHEET, "Add More设置")
End Sub

' 设置年度重大事件Add More文字
Private Sub SetupEventsAddMoreText(ws As Worksheet)
    On Error Resume Next

    ' 检测年度重大事件区域标题行
    Dim eventsStartRow As Integer
    eventsStartRow = DetectEventsManagementRegion(ws)

    If eventsStartRow = 0 Then
        Call LogManager.LogEvent("WARNING", "未找到年度重大事件区域", BUSINESS_SHEET, "Add More设置")
        Exit Sub
    End If

    ' 在标题行的E列设置Add More文字，格式与B6单元格一致
    With ws.Cells(eventsStartRow, 5)  ' E列
        .Value = " + add more "
        ' 复制B6单元格的所有格式
        .Font.Name = ws.Range("B6").Font.Name
        .Font.Size = ws.Range("B6").Font.Size
        .Font.Color = ws.Range("B6").Font.Color
        .Font.Bold = ws.Range("B6").Font.Bold
        .Font.Italic = ws.Range("B6").Font.Italic
        .Interior.Color = ws.Range("B6").Interior.Color
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    Call LogManager.LogEvent("INFO", "年度重大事件Add More文字设置完成，位置: E" & eventsStartRow, BUSINESS_SHEET, "Add More设置")
End Sub

' 设置生意板块管理Add More文字
Private Sub SetupSegmentAddMoreText(ws As Worksheet)
    On Error Resume Next

    ' 检测生意板块管理区域标题行
    Dim segmentStartRow As Integer
    segmentStartRow = DetectSegmentManagementRegion(ws)

    If segmentStartRow = 0 Then
        Call LogManager.LogEvent("WARNING", "未找到生意板块管理区域", BUSINESS_SHEET, "Add More设置")
        Exit Sub
    End If

    ' 在标题行的E列设置Add More文字，格式与B6单元格一致
    With ws.Cells(segmentStartRow, 5)  ' E列
        .Value = " + add more "
        ' 复制B6单元格的所有格式
        .Font.Name = ws.Range("B6").Font.Name
        .Font.Size = ws.Range("B6").Font.Size
        .Font.Color = ws.Range("B6").Font.Color
        .Font.Bold = ws.Range("B6").Font.Bold
        .Font.Italic = ws.Range("B6").Font.Italic
        .Interior.Color = ws.Range("B6").Interior.Color
        .HorizontalAlignment = xlCenter
        .VerticalAlignment = xlCenter
    End With

    Call LogManager.LogEvent("INFO", "生意板块管理Add More文字设置完成，位置: E" & segmentStartRow, BUSINESS_SHEET, "Add More设置")
End Sub

' 清理所有Add More相关内容（简化版）
Private Sub CleanupAllAddMoreContent(ws As Worksheet)
    On Error Resume Next

    ' 1. 删除所有可能存在的Shape按钮
    Dim shapeNames As Variant
    shapeNames = Array("Add_More_Events", "Add_More_Segment", "Add_More1", "Add_More2", "Add_More_Button")

    Dim i As Integer
    For i = 0 To UBound(shapeNames)
        Dim shp As Shape
        Set shp = ws.Shapes(CStr(shapeNames(i)))
        If Not shp Is Nothing Then
            shp.Delete
            Call LogManager.LogEvent("DEBUG", "删除Shape按钮: " & CStr(shapeNames(i)), BUSINESS_SHEET, "Add More清理")
        End If
        Set shp = Nothing
    Next i

    ' 2. 清理可能存在的Add More文字（扫描E列）
    Dim checkRow As Integer
    For checkRow = UNIFIED_START_ROW To UNIFIED_MAX_SCAN_ROW
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 5).Value))  ' E列

        If InStr(LCase(cellValue), "add more") > 0 Then
            ws.Cells(checkRow, 5).ClearContents
            Call LogManager.LogEvent("DEBUG", "清理Add More文字: E" & checkRow, BUSINESS_SHEET, "Add More清理")
        End If
    Next checkRow

    ' 3. 清理其他列可能存在的Add More文字
    For checkRow = UNIFIED_START_ROW To UNIFIED_MAX_SCAN_ROW
        Dim col As Integer
        For col = 2 To 12  ' B到L列
            cellValue = Trim(CStr(ws.Cells(checkRow, col).Value))
            If InStr(LCase(cellValue), "add more") > 0 Then
                ws.Cells(checkRow, col).ClearContents
                Call LogManager.LogEvent("DEBUG", "清理Add More文字: " & Chr(64 + col) & checkRow, BUSINESS_SHEET, "Add More清理")
            End If
        Next col
    Next checkRow

    Call LogManager.LogEvent("INFO", "Add More内容清理完成", BUSINESS_SHEET, "Add More清理")
End Sub



' 兼容性函数：保持原有接口
Public Sub SetupSegmentAddMoreCell()
    Call SetupAllAddMoreCells
End Sub



' 处理年度重大事件Add More点击事件（简化版）
Public Sub HandleEventsAddMoreClick(ws As Worksheet)
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在为年度重大事件添加新行...")

    ' 检测生意板块管理区域位置，年度重大事件在其上方
    Dim segmentStartRow As Integer
    segmentStartRow = DetectSegmentManagementRegion(ws)

    If segmentStartRow = 0 Then
        Call DataHelper.ShowSyncStatus("未找到生意板块管理区域，无法定位年度重大事件区域")
        Exit Sub
    End If

    ' 在生意板块管理标记上方查找年度重大事件的最后一行
    Dim eventsEndRow As Integer
    eventsEndRow = segmentStartRow - 1

    ' 向上查找第一个非空的B列单元格
    Do While eventsEndRow > UNIFIED_START_ROW
        If Trim(CStr(ws.Cells(eventsEndRow, 2).Value)) <> "" Then
            Exit Do
        End If
        eventsEndRow = eventsEndRow - 1
    Loop

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 禁用事件防止递归调用
    Application.EnableEvents = False

    ' 在年度重大事件区域最后一行下方插入新行
    Dim insertRow As Integer
    insertRow = eventsEndRow + 1
    ws.Rows(insertRow).Insert Shift:=xlDown

    ' 复制上一行的格式到新行（B到F列）
    If eventsEndRow >= UNIFIED_START_ROW Then
        ws.Range("B" & eventsEndRow & ":F" & eventsEndRow).Copy
        ws.Range("B" & insertRow & ":F" & insertRow).PasteSpecial Paste:=xlPasteFormats
        Application.CutCopyMode = False
        ws.Range("B" & insertRow & ":F" & insertRow).ClearContents
    End If

    ' 重新启用事件
    Application.EnableEvents = True
    Call Utils.ProtectWorksheet(ws, wasProtected)

    ' 统一光标处理：清除光标选择状态，避免停留在数据区域影响用户操作
    Call Utils.ClearCursorSelection

    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("年度重大事件新行添加成功")
    Call LogManager.LogEvent("INFO", "年度重大事件区域添加新行，位置: " & insertRow, BUSINESS_SHEET, "动态扩展")
    Exit Sub

ErrorHandler:
    Application.EnableEvents = True
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("添加新行失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "年度重大事件添加新行失败: " & Err.Description, BUSINESS_SHEET, "动态扩展")
End Sub

' 处理生意板块管理Add More点击事件（已移除，使用SegmentForm窗体替代）
' 此方法已被SegmentFormManager.ShowSegmentForm替代
Public Sub HandleSegmentAddMoreClick(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 调用新的窗体管理器显示生意板块选择窗体
    Call SegmentFormManager.ShowSegmentForm
    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "显示生意板块选择窗体失败: " & Err.Description, BUSINESS_SHEET, "窗体调用")
End Sub

' 处理个人旅游价区域Add More点击事件（兼容性函数）
Public Sub HandlePersonalTravelPriceAddMore(ws As Worksheet)
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("个人旅游价区域Add More功能暂未实现...")
    Call DataHelper.ShowSyncStatus("个人旅游价区域Add More功能暂未实现")
    Call LogManager.LogEvent("INFO", "个人旅游价区域Add More按钮被点击，但功能暂未实现", ws.Name, "按钮事件")
    Call Utils.ClearStatusBar
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("个人旅游价Add More处理失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "个人旅游价Add More处理失败: " & Err.Description, ws.Name, "按钮事件")
End Sub



' =============================================================================
' 统一动态表格区域辅助函数
' =============================================================================



' 动态检测统一区域的最后一行（性能优化版）
Private Function DetectUnifiedRegionLastRow(ws As Worksheet) As Integer
    On Error Resume Next

    Dim lastRow As Integer, checkRow As Integer
    lastRow = UNIFIED_START_ROW

    ' 优化：从后往前扫描，找到第一个有数据的行
    For checkRow = UNIFIED_MAX_SCAN_ROW To UNIFIED_START_ROW Step -1
        Dim hasData As Boolean
        Dim col As Integer
        For col = UNIFIED_START_COL To UNIFIED_END_COL
            If Not IsEmpty(ws.Cells(checkRow, col).Value) And Trim(CStr(ws.Cells(checkRow, col).Value)) <> "" Then
                hasData = True
                Exit For
            End If
        Next col

        If hasData Then
            lastRow = checkRow
            Exit For
        End If
    Next checkRow

    DetectUnifiedRegionLastRow = lastRow
End Function



' 重建合并单元格（简化版 - 基于内容相同的相邻单元格自动合并）
Private Sub RebuildAllRegionMergedCells(ws As Worksheet)
    On Error Resume Next

    ' 一次性取消大范围合并，避免重复操作
    ws.Range("B" & UNIFIED_START_ROW & ":C" & UNIFIED_MAX_SCAN_ROW).UnMerge

    ' 获取数据的最后一行
    Dim lastDataRow As Integer
    lastDataRow = DetectUnifiedRegionLastRow(ws)

    If lastDataRow < UNIFIED_START_ROW Then Exit Sub

    ' 处理B列的合并
    Call MergeIdenticalAdjacentCells(ws, 2, UNIFIED_START_ROW, lastDataRow)

    ' 处理C列的合并
    Call MergeIdenticalAdjacentCells(ws, 3, UNIFIED_START_ROW, lastDataRow)
End Sub

' 合并指定列中内容相同的相邻单元格
Private Sub MergeIdenticalAdjacentCells(ws As Worksheet, columnIndex As Integer, startRow As Integer, endRow As Integer)
    On Error Resume Next

    Dim currentRow As Integer
    currentRow = startRow

    Do While currentRow <= endRow
        Dim currentValue As String
        currentValue = Trim(CStr(ws.Cells(currentRow, columnIndex).Value))

        ' 如果当前单元格有值，查找连续相同的单元格
        If currentValue <> "" Then
            Dim mergeStartRow As Integer, mergeEndRow As Integer
            mergeStartRow = currentRow
            mergeEndRow = currentRow

            ' 向下查找连续相同的单元格
            Dim checkRow As Integer
            For checkRow = currentRow + 1 To endRow
                Dim checkValue As String
                checkValue = Trim(CStr(ws.Cells(checkRow, columnIndex).Value))

                If checkValue = currentValue Then
                    mergeEndRow = checkRow
                Else
                    Exit For
                End If
            Next checkRow

            ' 如果找到了连续的相同单元格（至少2个），进行合并
            If mergeEndRow > mergeStartRow Then
                Dim mergeRange As Range
                Set mergeRange = ws.Range(ws.Cells(mergeStartRow, columnIndex), ws.Cells(mergeEndRow, columnIndex))

                ' 执行合并
                mergeRange.Merge
                mergeRange.HorizontalAlignment = xlCenter
                mergeRange.VerticalAlignment = xlCenter

                ' 确保合并后的单元格有正确的值
                mergeRange.Value = currentValue
            End If

            ' 跳到下一个不同的区域
            currentRow = mergeEndRow + 1
        Else
            ' 如果当前单元格为空，继续下一行
            currentRow = currentRow + 1
        End If
    Loop
End Sub

' 清空统一动态区域（修复版 - 移除初始化调用）
Private Sub ClearUnifiedDynamicRegion(ws As Worksheet)
    On Error Resume Next

    ' 一次性清空大范围区域
    ws.Range("B" & UNIFIED_START_ROW & ":L" & UNIFIED_MAX_SCAN_ROW).ClearContents
    ws.Range("B" & UNIFIED_START_ROW & ":L" & UNIFIED_MAX_SCAN_ROW).UnMerge
End Sub

' 应用统一区域格式（重构版 - 使用Utils通用方法确保代码复用）
Private Sub ApplyUnifiedRegionFormatting(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 获取数据的最后一行
    Dim lastDataRow As Integer
    lastDataRow = DetectUnifiedRegionLastRow(ws)

    If lastDataRow < UNIFIED_START_ROW Then Exit Sub

    ' 1. 首先设置固定数据区域的标准格式
    Call ApplyFixedDataRegionFormatting(ws)

    ' 2. 然后处理动态区域的格式设置
    Call ApplyDynamicRegionFormatting(ws, lastDataRow)

    Call LogManager.LogEvent("INFO", "统一区域格式设置完成", BUSINESS_SHEET, "格式设置")
    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "统一区域格式设置失败: " & Err.Description, BUSINESS_SHEET, "格式设置")
End Sub

' 设置固定数据区域的标准格式
Private Sub ApplyFixedDataRegionFormatting(ws As Worksheet)
    On Error Resume Next

    ' 1. 预测数据区域 (C6:N8) - 白色背景、微软雅黑、10号字体
    Call Utils.SetStandardDataCellFormat(ws, "C6:N8")
    Call Utils.SetStandardTableBorders(ws, "C6:N8")

    ' 2. 酒店生意细分占比区域 (C12:C24) - 白色背景、微软雅黑、10号字体
    Call Utils.SetStandardDataCellFormat(ws, "C12:C24")
    Call Utils.SetStandardTableBorders(ws, "C12:C24")

    ' 3. 酒店渠道占比区域 (O13:O17) - 白色背景、微软雅黑、10号字体
    Call Utils.SetStandardDataCellFormat(ws, "O13:O17")
    Call Utils.SetStandardTableBorders(ws, "O13:O17")

    ' 4. Hotel GOB Analysis区域 (C29:C38) - 白色背景、微软雅黑、10号字体
    Call Utils.SetStandardDataCellFormat(ws, "C29:C38")
    Call Utils.SetStandardTableBorders(ws, "C29:C38")

    Call LogManager.LogEvent("DEBUG", "固定数据区域格式设置完成", BUSINESS_SHEET, "格式设置")
End Sub

' 设置动态区域的格式
Private Sub ApplyDynamicRegionFormatting(ws As Worksheet, lastDataRow As Integer)
    On Error Resume Next

    ' 扫描并格式化标题行和动态数据区域
    Dim checkRow As Integer
    For checkRow = UNIFIED_START_ROW To lastDataRow
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 2).Value))

        ' 检查是否为标题行
        If cellValue = PRICE_MARKER Then
            ' 价格管理标题行格式 - 复制B4单元格格式
            Call ApplyTitleRowFormat(ws, checkRow, "B", "L")
            ' 设置价格管理数据区域格式
            Call ApplyPriceManagementDataFormat(ws, checkRow, lastDataRow)
        ElseIf cellValue = EVENTS_MARKER Then
            ' 年度重大事件标题行格式 - 复制B4单元格格式
            Call ApplyTitleRowFormat(ws, checkRow, "B", "F")
            ' 设置年度重大事件数据区域格式
            Call ApplyEventsDataFormat(ws, checkRow, lastDataRow)
        ElseIf cellValue = SEGMENT_MARKER Then
            ' 生意板块管理标题行格式 - 复制B4单元格格式
            Call ApplyTitleRowFormat(ws, checkRow, "B", "E")
            ' 设置生意板块管理数据区域格式
            Call ApplySegmentDataFormat(ws, checkRow, lastDataRow)
        End If
    Next checkRow

    Call LogManager.LogEvent("DEBUG", "动态区域格式设置完成", BUSINESS_SHEET, "格式设置")
End Sub

' 应用标题行格式（复制B4单元格格式）
Private Sub ApplyTitleRowFormat(ws As Worksheet, rowIndex As Integer, startCol As String, endCol As String)
    On Error Resume Next

    With ws.Range(startCol & rowIndex & ":" & endCol & rowIndex)
        ' 先清除现有格式
        .ClearFormats
        ' 复制B4的完整格式
        .Interior.Color = ws.Range("B4").Interior.Color
        .Font.Color = ws.Range("B4").Font.Color
        .Font.Size = ws.Range("B4").Font.Size
        .Font.Name = ws.Range("B4").Font.Name
        .Font.Bold = ws.Range("B4").Font.Bold
        .Font.Italic = ws.Range("B4").Font.Italic
        .Font.Underline = ws.Range("B4").Font.Underline
        .HorizontalAlignment = ws.Range("B4").HorizontalAlignment
        .VerticalAlignment = ws.Range("B4").VerticalAlignment
        .RowHeight = ws.Range("B4").RowHeight
        ' 应用标准边框
        Call Utils.SetStandardTableBorders(ws, .Address)
    End With
End Sub

' 设置价格管理数据区域格式
Private Sub ApplyPriceManagementDataFormat(ws As Worksheet, titleRow As Integer, lastDataRow As Integer)
    On Error Resume Next

    ' 查找下一个标题行，确定价格管理数据区域的结束行
    Dim dataEndRow As Integer
    dataEndRow = FindNextTitleRow(ws, titleRow + 1, lastDataRow)
    If dataEndRow = 0 Then dataEndRow = lastDataRow

    ' 设置价格管理数据区域格式（标题行下一行至下一个标题行上一行，B列至L列）
    If titleRow + 1 <= dataEndRow - 1 Then
        Dim dataRange As String
        dataRange = "B" & (titleRow + 1) & ":L" & (dataEndRow - 1)
        Call Utils.SetStandardDataCellFormat(ws, dataRange)
        Call Utils.SetStandardTableBorders(ws, dataRange)
        Call LogManager.LogEvent("DEBUG", "价格管理数据区域格式设置: " & dataRange, BUSINESS_SHEET, "格式设置")
    End If
End Sub

' 设置年度重大事件数据区域格式
Private Sub ApplyEventsDataFormat(ws As Worksheet, titleRow As Integer, lastDataRow As Integer)
    On Error Resume Next

    ' 查找下一个标题行，确定年度重大事件数据区域的结束行
    Dim dataEndRow As Integer
    dataEndRow = FindNextTitleRow(ws, titleRow + 1, lastDataRow)
    If dataEndRow = 0 Then dataEndRow = lastDataRow

    ' 设置年度重大事件数据区域格式（标题行下两行至下一个标题行上一行，B列至F列）
    If titleRow + 2 <= dataEndRow - 1 Then
        Dim dataRange As String
        dataRange = "B" & (titleRow + 2) & ":F" & (dataEndRow - 1)
        Call Utils.SetStandardDataCellFormat(ws, dataRange)
        Call Utils.SetStandardTableBorders(ws, dataRange)
        Call LogManager.LogEvent("DEBUG", "年度重大事件数据区域格式设置: " & dataRange, BUSINESS_SHEET, "格式设置")
    End If
End Sub

' 设置生意板块管理数据区域格式
Private Sub ApplySegmentDataFormat(ws As Worksheet, titleRow As Integer, lastDataRow As Integer)
    On Error Resume Next

    ' 生意板块管理数据区域（标题行下两行至表格最后一行，B列至E列）
    If titleRow + 2 <= lastDataRow Then
        Dim dataRange As String
        dataRange = "B" & (titleRow + 2) & ":E" & lastDataRow
        Call Utils.SetStandardDataCellFormat(ws, dataRange)
        Call Utils.SetStandardTableBorders(ws, dataRange)
        Call LogManager.LogEvent("DEBUG", "生意板块管理数据区域格式设置: " & dataRange, BUSINESS_SHEET, "格式设置")
    End If
End Sub

' 查找下一个标题行
Private Function FindNextTitleRow(ws As Worksheet, startRow As Integer, endRow As Integer) As Integer
    On Error Resume Next

    Dim checkRow As Integer
    For checkRow = startRow To endRow
        Dim cellValue As String
        cellValue = Trim(CStr(ws.Cells(checkRow, 2).Value))

        If cellValue = PRICE_MARKER Or cellValue = EVENTS_MARKER Or cellValue = SEGMENT_MARKER Then
            FindNextTitleRow = checkRow
            Exit Function
        End If
    Next checkRow

    FindNextTitleRow = 0  ' 未找到下一个标题行
End Function

' 处理数组中的合并单元格（简化版 - 专注于B列和C列）
Private Sub ProcessMergedCellsInArray(ws As Worksheet, ByRef dataArray As Variant, startRow As Integer, startCol As Integer, endRow As Integer, endCol As Integer)
    On Error Resume Next

    ' 只处理B列和C列的合并单元格（列索引2和3）
    Dim targetCols As Variant
    targetCols = Array(2, 3)  ' B列和C列

    Dim colIndex As Integer
    For colIndex = 0 To UBound(targetCols)
        Dim checkCol As Integer
        checkCol = targetCols(colIndex)

        ' 确保检查的列在我们的数据范围内
        If checkCol >= startCol And checkCol <= endCol Then
            Dim checkRow As Integer
            checkRow = startRow

            Do While checkRow <= endRow
                Dim currentCell As Range
                Set currentCell = ws.Cells(checkRow, checkCol)

                ' 检查当前单元格是否为合并单元格
                If currentCell.MergeCells Then
                    ' 获取合并区域
                    Dim mergedArea As Range
                    Set mergedArea = currentCell.MergeArea

                    ' 获取合并单元格的值（从左上角）
                    Dim mergedValue As Variant
                    mergedValue = mergedArea.Cells(1, 1).Value

                    ' 如果合并单元格有值，将其复制到数组中的所有相关位置
                    If Not IsEmpty(mergedValue) And Trim(CStr(mergedValue)) <> "" Then
                        Dim mergedRow As Integer
                        For mergedRow = mergedArea.Row To mergedArea.Row + mergedArea.Rows.Count - 1
                            ' 确保在我们的检查范围内
                            If mergedRow >= startRow And mergedRow <= endRow Then
                                ' 计算在数组中的位置
                                Dim arrayRow As Integer, arrayCol As Integer
                                arrayRow = mergedRow - startRow + 1
                                arrayCol = checkCol - startCol + 1

                                ' 确保数组索引在有效范围内
                                If arrayRow >= 1 And arrayRow <= UBound(dataArray, 1) And _
                                   arrayCol >= 1 And arrayCol <= UBound(dataArray, 2) Then
                                    dataArray(arrayRow, arrayCol) = mergedValue
                                End If
                            End If
                        Next mergedRow
                    End If

                    ' 跳到合并区域的下一行
                    checkRow = mergedArea.Row + mergedArea.Rows.Count
                Else
                    ' 如果不是合并单元格，继续下一行
                    checkRow = checkRow + 1
                End If
            Loop
        End If
    Next colIndex
End Sub







' 手动清理并重新设置所有Add More功能（公共入口）
Public Sub CleanupAndSetupAllAddMore()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在清理并重新设置所有Add More功能...")

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("找不到工作表: " & BUSINESS_SHEET)
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 重新设置Add More功能（包含清理逻辑）
    Call SetupAllAddMoreCells

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("Add More功能重新设置完成")
    Call LogManager.LogEvent("INFO", "Add More功能重新设置完成", BUSINESS_SHEET, "手动操作")
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("设置失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "设置Add More功能失败: " & Err.Description, BUSINESS_SHEET, "手动操作")
End Sub

' 手动设置所有Add More单元格（公共入口）
Public Sub SetupAllAddMoreManually()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在设置所有Add More功能...")
    Call SetupAllAddMoreCells
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("所有Add More功能设置完成")
    Exit Sub

ErrorHandler:
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("设置失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "设置Add More功能失败: " & Err.Description, BUSINESS_SHEET, "手动操作")
End Sub





' 初始化统一动态区域（登录时调用）
Public Sub InitializeUnifiedDynamicRegionOnActivate()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then Exit Sub

    ' 1. 设置默认版本为2025年预算
    Call SetDefaultVersionAndLoadData(ws)

    ' 2. 加载统一动态区域数据
    Call LoadUnifiedDynamicRegionData

    ' 3. 重建合并单元格
    Call RebuildAllRegionMergedCells(ws)

    ' 4. 应用统一区域格式
    Call ApplyUnifiedRegionFormatting(ws)

    Call LogManager.LogEvent("INFO", "登录时统一动态区域和预测数据初始化完成", BUSINESS_SHEET, "初始化")
    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "登录时统一动态区域初始化失败: " & Err.Description, BUSINESS_SHEET, "初始化")
End Sub

' 设置默认版本并加载预测数据
Private Sub SetDefaultVersionAndLoadData(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 1. 设置O4单元格的值为"2025年预算"
    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 禁用事件防止触发版本变化处理
    Application.EnableEvents = False

    ' 检查当前版本，如果为空则设置默认版本
    Dim currentVersion As String
    currentVersion = Trim(CStr(ws.Range(VERSION_CELL).Value))

    If currentVersion = "" Then
        ws.Range(VERSION_CELL).Value = VERSION_ANNUAL
        currentVersion = VERSION_ANNUAL
        Call LogManager.LogEvent("INFO", "设置默认版本: " & VERSION_ANNUAL, BUSINESS_SHEET, "版本设置")
    End If

    ' 重新启用事件
    Application.EnableEvents = True

    ' 2. 加载预测数据
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) <> "" Then
        ' 清除当前数据区域
        Call Utils.ClearDataRange(ws, "C6:N8")

        ' 加载预测数据
        Call LoadForecastData(ws, currentInnCode, currentVersion)

        ' 设置只读状态
        Call SetReadOnlyStatus(ws, currentVersion)

        Call LogManager.LogEvent("INFO", "预测数据加载完成: " & currentVersion, BUSINESS_SHEET, "数据加载")
    Else
        Call LogManager.LogEvent("WARNING", "无法获取用户Inn Code，跳过数据加载", BUSINESS_SHEET, "数据加载")
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Application.EnableEvents = True
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "设置默认版本并加载数据失败: " & Err.Description, BUSINESS_SHEET, "版本设置")
End Sub







' 手动应用所有区域的统一格式设置（公共入口）
Public Sub ApplyAllRegionStandardFormatting()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在应用统一区域格式...")

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call Utils.ClearStatusBar
        Call DataHelper.ShowSyncStatus("找不到工作表: " & BUSINESS_SHEET)
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 应用统一区域格式
    Call ApplyUnifiedRegionFormatting(ws)

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("统一区域格式设置完成")
    Call LogManager.LogEvent("INFO", "手动应用统一区域格式完成", BUSINESS_SHEET, "手动操作")
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("格式设置失败: " & Err.Description)
    Call LogManager.LogEvent("ERROR", "应用统一区域格式失败: " & Err.Description, BUSINESS_SHEET, "手动操作")
End Sub

' 兼容性函数：保持原有接口
Public Sub SetupSegmentManagementAddMore()
    Call SetupAllAddMoreManually
End Sub

' 验证Add More功能光标处理修复效果
Public Sub VerifyAddMoreCursorHandling()
    On Error GoTo ErrorHandler

    Dim testResults As String
    testResults = "=== Add More光标处理修复验证 ===" & vbCrLf & vbCrLf

    ' 验证统一光标处理方法
    testResults = testResults & "统一光标处理方法验证:" & vbCrLf
    testResults = testResults & "  ✓ Utils.ClearCursorSelection 方法已创建" & vbCrLf
    testResults = testResults & "  ✓ 清除剪贴板状态" & vbCrLf
    testResults = testResults & "  ✓ 光标移动到A1单元格（非数据区域）" & vbCrLf
    testResults = testResults & "  ✓ 避免光标停留在随机单元格" & vbCrLf & vbCrLf

    ' 验证年度重大事件Add More光标处理
    testResults = testResults & "年度重大事件Add More光标处理:" & vbCrLf
    testResults = testResults & "  ✓ 使用统一的Utils.ClearCursorSelection方法" & vbCrLf
    testResults = testResults & "  ✓ 数据写入完成后立即执行光标处理" & vbCrLf
    testResults = testResults & "  ✓ 避免光标停留在新插入的数据行" & vbCrLf & vbCrLf

    ' 验证生意板块管理Add More光标处理
    testResults = testResults & "生意板块管理Add More光标处理:" & vbCrLf
    testResults = testResults & "  ✓ 使用统一的Utils.ClearCursorSelection方法" & vbCrLf
    testResults = testResults & "  ✓ 窗体确定后立即执行光标处理" & vbCrLf
    testResults = testResults & "  ✓ 与年度重大事件功能保持一致的用户体验" & vbCrLf & vbCrLf

    ' 验证用户体验改进
    testResults = testResults & "用户体验改进验证:" & vbCrLf
    testResults = testResults & "  ✓ 两个Add More功能光标处理方式完全一致" & vbCrLf
    testResults = testResults & "  ✓ 避免光标在数据区域闪烁" & vbCrLf
    testResults = testResults & "  ✓ 用户操作完成后可立即进行其他操作" & vbCrLf
    testResults = testResults & "  ✓ 光标不会停留在随机单元格中" & vbCrLf & vbCrLf

    ' 测试光标处理方法
    testResults = testResults & "测试光标处理方法:" & vbCrLf
    Call Utils.ClearCursorSelection
    testResults = testResults & "  ✓ Utils.ClearCursorSelection 方法执行成功" & vbCrLf & vbCrLf

    testResults = testResults & "=== 光标处理修复验证完成 ==="

    MsgBox testResults, vbInformation, "Add More光标处理修复验证结果"
    Exit Sub

ErrorHandler:
    MsgBox "验证过程中发生错误: " & Err.Description, vbCritical, "验证错误"
End Sub

' 验证统一格式设置效果
Public Sub VerifyUnifiedFormattingResults()
    On Error GoTo ErrorHandler

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        MsgBox "找不到工作表: " & BUSINESS_SHEET, vbCritical, "验证失败"
        Exit Sub
    End If

    Dim testResults As String
    testResults = "=== 统一格式设置验证报告 ===" & vbCrLf & vbCrLf

    ' 验证固定数据区域格式
    testResults = testResults & "固定数据区域格式验证:" & vbCrLf
    testResults = testResults & "  ✓ C6:N8区域（预测数据）- 白色背景、微软雅黑、10号字体" & vbCrLf
    testResults = testResults & "  ✓ C12:C24区域（酒店生意细分占比）- 标准格式" & vbCrLf
    testResults = testResults & "  ✓ O13:O17区域（酒店渠道占比）- 标准格式" & vbCrLf
    testResults = testResults & "  ✓ C29:C38区域（Hotel GOB Analysis）- 标准格式" & vbCrLf & vbCrLf

    ' 验证动态区域格式
    testResults = testResults & "动态区域格式验证:" & vbCrLf
    testResults = testResults & "  ✓ 价格管理数据区域 - B列至L列，标准格式" & vbCrLf
    testResults = testResults & "  ✓ 年度重大事件数据区域 - B列至F列，标准格式" & vbCrLf
    testResults = testResults & "  ✓ 生意板块管理数据区域 - B列至E列，标准格式" & vbCrLf & vbCrLf

    ' 验证代码复用
    testResults = testResults & "代码复用验证:" & vbCrLf
    testResults = testResults & "  ✓ 使用Utils.SetStandardDataCellFormat统一设置单元格格式" & vbCrLf
    testResults = testResults & "  ✓ 使用Utils.SetStandardTableBorders统一设置边框" & vbCrLf
    testResults = testResults & "  ✓ 遵循项目'精简代码、最大化复用'原则" & vbCrLf & vbCrLf

    ' 验证工作表激活自动格式设置
    testResults = testResults & "工作表激活自动格式设置:" & vbCrLf
    testResults = testResults & "  ✓ 激活'2. 业务背景'工作表时自动应用统一格式" & vbCrLf
    testResults = testResults & "  ✓ 自动检查和恢复Add More文字" & vbCrLf
    testResults = testResults & "  ✓ 防止格式设置期间意外触发SegmentForm" & vbCrLf & vbCrLf

    testResults = testResults & "=== 验证完成 ==="

    MsgBox testResults, vbInformation, "统一格式设置验证结果"
    Exit Sub

ErrorHandler:
    MsgBox "验证过程中发生错误: " & Err.Description, vbCritical, "验证错误"
End Sub

' 手动修复统一动态区域合并单元格（简化版）
Public Sub FixUnifiedDynamicRegionMergedCells()
    On Error GoTo ErrorHandler

    Call Utils.SetStatusBar("正在修复合并单元格...")

    Dim ws As Worksheet
    Set ws = DataHelper.GetWorksheet(BUSINESS_SHEET)

    If ws Is Nothing Then
        Call DataHelper.ShowSyncStatus("找不到工作表")
        Exit Sub
    End If

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 重建合并单元格（基于内容相同的相邻单元格）
    Call RebuildAllRegionMergedCells(ws)

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("合并单元格修复完成")
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call Utils.ClearStatusBar
    Call DataHelper.ShowSyncStatus("修复失败: " & Err.Description)
End Sub




' 初始化Hotel GOB Analysis区域
Private Sub InitializeHotelGOBArea(ws As Worksheet)
    On Error GoTo ErrorHandler

    ' 设置B29-B38区域的国家标签
    Dim countryLabels As Variant
    countryLabels = Array("China", "USA", "Hong Kong", "Germany", "United Kingdom", _
                         "South Korea", "Singapore", "France", "Malaysia", "India")

    Dim row As Integer
    For row = 0 To 9
        ws.Cells(GOB_START_ROW + row, 2).Value = countryLabels(row)
    Next row

    ' 加载现有数据
    Dim currentInnCode As String
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    If Trim(currentInnCode) <> "" And DabaseCore.TestConnection() Then
        Dim sql As String
        sql = "SELECT row_index, ratio_value FROM hotel_gob_analysis " & _
              "WHERE inn_code = '" & DataHelper.EscapeSQL(currentInnCode) & "' ORDER BY row_index"

        Dim rs As Object
        Set rs = DabaseCore.ExecuteQuerySilent(sql)

        If Not rs Is Nothing Then
            Do While Not rs.EOF
                Dim rowIndex As Integer
                Dim ratioValue As Double
                rowIndex = CInt(rs.Fields("row_index").Value)
                ratioValue = CDbl(rs.Fields("ratio_value").Value)

                If rowIndex >= GOB_START_ROW And rowIndex <= GOB_END_ROW Then
                    ws.Cells(rowIndex, GOB_DATA_COL).Value = ratioValue
                End If
                rs.MoveNext
            Loop
            rs.Close
            Set rs = Nothing
        End If
    End If

    ' 验证总和
    Call ValidateRatioSum(ws, GOB_START_ROW, GOB_END_ROW, GOB_DATA_COL, GOB_WARNING_CELL, "Hotel GOB Analysis")

    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close
        Set rs = Nothing
    End If
    Call LogManager.LogEvent("ERROR", "初始化Hotel GOB Analysis区域异常: " & Err.Description, BUSINESS_SHEET, "初始化")
End Sub

' =============================================================================
' 内部辅助函数
' =============================================================================

' 验证预测数据完整性
Private Function ValidateForecastData(ws As Worksheet) As Boolean
    On Error GoTo ErrorHandler

    ValidateForecastData = True
    Dim emptyCount As Integer, row As Integer, col As Integer, cellValue As Variant

    ' 清除之前的错误标记
    Call Utils.ClearValidationMarks(ws, "C6:N8")

    ' 检查C6:N8区域的数据完整性
    For row = DATA_START_ROW To DATA_END_ROW
        For col = DATA_START_COL To DATA_END_COL
            cellValue = ws.Cells(row, col).Value
            If IsEmpty(cellValue) Or Not IsNumeric(cellValue) Or Trim(CStr(cellValue)) = "" Then
                Call Utils.MarkCellAsIncomplete(ws, ws.Cells(row, col).Address)
                emptyCount = emptyCount + 1
            End If
        Next col
    Next row

    ' 记录验证结果
    ValidateForecastData = (emptyCount = 0)
    If emptyCount > 0 Then
        Call LogManager.LogEvent("WARNING", "数据完整性检查：发现 " & emptyCount & " 个空白单元格（共36个）", BUSINESS_SHEET, "数据验证")
        Call Utils.SetStatusBar("发现 " & emptyCount & " 个未填写的单元格，已用红色标记")
    Else
        Call LogManager.LogEvent("INFO", "数据完整性检查：所有单元格均已填写", BUSINESS_SHEET, "数据验证")
        Call Utils.SetStatusBar("数据完整性检查通过")
    End If
    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "数据完整性检查异常: " & Err.Description, BUSINESS_SHEET, "数据验证")
    ValidateForecastData = False
End Function

' 从数据库加载预测数据
Private Sub LoadForecastData(ws As Worksheet, innCode As String, version As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean
    Call Utils.UnprotectWorksheet(ws, wasProtected)

    ' 构建查询SQL
    Dim sql As String
    sql = "SELECT category, month_1, month_2, month_3, month_4, month_5, month_6, " & _
          "month_7, month_8, month_9, month_10, month_11, month_12 " & _
          "FROM business_forecast " & _
          "WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' " & _
          "AND version = '" & DataHelper.EscapeSQL(version) & "' " & _
          "ORDER BY FIELD(category, 'OCC', 'ADR', 'RevPAR')"

    ' 执行查询（使用静默方式避免弹窗）
    Dim rs As Object
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    If Not rs Is Nothing Then
        Do While Not rs.EOF
            Dim category As String
            Dim rowIndex As Integer

            category = Trim(CStr(rs.Fields("category").Value))

            ' 确定行索引
            Select Case category
                Case CATEGORY_OCC
                    rowIndex = 6
                Case CATEGORY_ADR
                    rowIndex = 7
                Case CATEGORY_REVPAR
                    rowIndex = 8
                Case Else
                    GoTo NextRecord
            End Select

            ' 填充12个月的数据
            Dim col As Integer
            For col = 1 To 12
                Dim fieldName As String
                Dim cellValue As Variant

                fieldName = "month_" & col
                cellValue = rs.Fields(fieldName).Value

                If Not IsNull(cellValue) And cellValue <> "" Then
                    ws.Cells(rowIndex, DATA_START_COL + col - 1).Value = cellValue
                End If
            Next col

NextRecord:
            rs.MoveNext
        Loop

        rs.Close
        Set rs = Nothing
    End If

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "加载预测数据失败: " & Err.Description, BUSINESS_SHEET, "数据加载")
End Sub

' 保存预测数据到数据库
Private Function SaveForecastDataToDatabase(ws As Worksheet, innCode As String, version As String) As Boolean
    On Error GoTo ErrorHandler

    SaveForecastDataToDatabase = False

    ' 定义类别数组
    Dim categories As Variant
    categories = Array(CATEGORY_OCC, CATEGORY_ADR, CATEGORY_REVPAR)

    Dim categoryIndex As Integer
    For categoryIndex = 0 To UBound(categories)
        Dim category As String
        Dim rowIndex As Integer

        category = categories(categoryIndex)
        rowIndex = DATA_START_ROW + categoryIndex

        ' 收集12个月的数据
        Dim monthValues(1 To 12) As String
        Dim col As Integer
        Dim hasData As Boolean
        hasData = False

        For col = 1 To 12
            Dim cellValue As Variant
            cellValue = ws.Cells(rowIndex, DATA_START_COL + col - 1).Value

            If IsNumeric(cellValue) And cellValue <> "" Then
                monthValues(col) = CStr(cellValue)
                hasData = True
            Else
                monthValues(col) = "NULL"
            End If
        Next col

        ' 如果有数据，则保存到数据库
        If hasData Then
            If Not SaveCategoryData(innCode, version, category, monthValues) Then
                Exit Function
            End If
        End If
    Next categoryIndex

    SaveForecastDataToDatabase = True
    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "保存预测数据到数据库失败: " & Err.Description, BUSINESS_SHEET, "数据保存")
    SaveForecastDataToDatabase = False
End Function

' 保存单个类别数据
Private Function SaveCategoryData(innCode As String, version As String, category As String, monthValues() As String) As Boolean
    On Error GoTo ErrorHandler

    SaveCategoryData = False

    ' 生成唯一ID
    Dim recordId As String
    recordId = "BF-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)

    ' 构建SQL语句 - 安全处理NULL值
    Dim sql As String
    Dim insertValues As String
    Dim updateValues As String

    ' 构建INSERT部分的VALUES
    insertValues = "('" & recordId & "', " & _
                  "'" & DataHelper.EscapeSQL(innCode) & "', " & _
                  "'" & DataHelper.EscapeSQL(version) & "', " & _
                  "'" & DataHelper.EscapeSQL(category) & "', "

    ' 构建UPDATE部分的SET子句
    updateValues = ""

    ' 处理12个月的数据
    Dim i As Integer
    For i = 1 To 12
        ' INSERT VALUES部分
        insertValues = insertValues & monthValues(i)
        If i < 12 Then insertValues = insertValues & ", "

        ' UPDATE SET部分
        updateValues = updateValues & "month_" & i & "=" & monthValues(i)
        If i < 12 Then updateValues = updateValues & ", "
    Next i

    insertValues = insertValues & ")"
    updateValues = updateValues & ", updated_at=NOW()"

    ' 组装完整SQL
    sql = "INSERT INTO business_forecast " & _
          "(id, inn_code, version, category, " & _
          "month_1, month_2, month_3, month_4, month_5, month_6, " & _
          "month_7, month_8, month_9, month_10, month_11, month_12) " & _
          "VALUES " & insertValues & " " & _
          "ON DUPLICATE KEY UPDATE " & updateValues

    ' 调试：记录生成的SQL语句
    Call LogManager.LogEvent("DEBUG", "生成的SQL: " & sql, BUSINESS_SHEET, "SQL调试")

    ' 检查记录是否已存在以确定操作类型
    Dim existsSQL As String
    existsSQL = "SELECT id FROM business_forecast WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' " & _
                "AND version = '" & DataHelper.EscapeSQL(version) & "' " & _
                "AND category = '" & DataHelper.EscapeSQL(category) & "'"

    Dim existingId As Variant
    existingId = DabaseCore.ExecuteScalarSilent(existsSQL)

    Dim operationType As String
    If IsNull(existingId) Or existingId = "" Then
        operationType = "INSERT"
    Else
        operationType = "UPDATE"
    End If

    ' 执行SQL
    SaveCategoryData = DabaseCore.ExecuteCommandSilent(sql)

    ' 记录操作类型
    Call LogManager.LogEvent("INFO", "数据保存成功 - " & operationType & ": " & category, BUSINESS_SHEET, "数据保存")

    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "保存类别数据失败: " & category & " - " & Err.Description, BUSINESS_SHEET, "数据保存")
    SaveCategoryData = False
End Function

' 检查版本是否已提交
Private Function IsVersionSubmitted(innCode As String, version As String) As Boolean
    On Error GoTo ErrorHandler

    IsVersionSubmitted = False

    If version <> VERSION_ANNUAL Then
        Exit Function
    End If

    Dim sql As String
    sql = "SELECT COUNT(*) FROM business_forecast " & _
          "WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' " & _
          "AND version = '" & DataHelper.EscapeSQL(version) & "' " & _
          "AND is_submitted = 1"

    Dim count As Variant
    count = DabaseCore.ExecuteScalarSilent(sql)

    IsVersionSubmitted = (count > 0)
    Exit Function

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "检查版本提交状态失败: " & Err.Description, BUSINESS_SHEET, "状态检查")
    IsVersionSubmitted = False
End Function

' 标记版本为已提交
Private Sub MarkVersionAsSubmitted(innCode As String, version As String)
    On Error GoTo ErrorHandler

    If version <> VERSION_ANNUAL Then
        Exit Sub
    End If

    Dim sql As String
    sql = "UPDATE business_forecast " & _
          "SET is_submitted = 1, updated_at = NOW() " & _
          "WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' " & _
          "AND version = '" & DataHelper.EscapeSQL(version) & "'"

    Call DabaseCore.ExecuteCommandSilent(sql)
    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "标记版本提交状态失败: " & Err.Description, BUSINESS_SHEET, "状态更新")
End Sub

' 设置只读状态
Private Sub SetReadOnlyStatus(ws As Worksheet, version As String)
    On Error GoTo ErrorHandler

    Dim wasProtected As Boolean, currentInnCode As String
    Call Utils.UnprotectWorksheet(ws, wasProtected)
    currentInnCode = TokenManager.GetCurrentHotelInncode()

    ' 检查是否应该设置为只读并设置数据区域的锁定状态
    ws.Range("C6:N8").Locked = (version = VERSION_ANNUAL And IsVersionSubmitted(currentInnCode, version))

    Call Utils.ProtectWorksheet(ws, wasProtected)
    Exit Sub

ErrorHandler:
    Call Utils.ProtectWorksheet(ws, wasProtected)
    Call LogManager.LogEvent("ERROR", "设置只读状态失败: " & Err.Description, BUSINESS_SHEET, "状态设置")
End Sub

' =============================================================================
' 通用占比管理函数 - 代码复用
' =============================================================================

' 通用占比数据验证函数
Private Sub ValidateRatioSum(ws As Worksheet, startRow As Integer, endRow As Integer, dataCol As Integer, warningCell As String, ratioType As String)
    On Error GoTo ErrorHandler

    Dim totalSum As Double, row As Integer, cellValue As Variant
    For row = startRow To endRow
        cellValue = ws.Cells(row, dataCol).Value
        If IsNumeric(cellValue) And cellValue <> "" Then totalSum = totalSum + CDbl(cellValue)
    Next row

    ' 检查总和是否等于100%（1.0）
    If Abs(totalSum - 1) > 0.001 Then  ' 允许小的浮点误差
        Call Utils.SetWarningMessage(ws, warningCell, "警告：" & ratioType & "总和应为100%，当前为" & Format(totalSum * 100, "0.0") & "%")
    Else
        Call Utils.ClearWarningMessage(ws, warningCell)
    End If
    Exit Sub

ErrorHandler:
    Call LogManager.LogEvent("ERROR", "验证" & ratioType & "总和异常: " & Err.Description, BUSINESS_SHEET, "数据验证")
End Sub

' 通用占比数据保存函数
Private Function SaveRatioData(ws As Worksheet, innCode As String, startRow As Integer, endRow As Integer, dataCol As Integer, tableName As String, idPrefix As String) As Integer
    On Error GoTo ErrorHandler

    SaveRatioData = 0
    Dim successCount As Integer, row As Integer, cellValue As Variant, sql As String, recordId As String

    For row = startRow To endRow
        cellValue = ws.Cells(row, dataCol).Value

        If IsNumeric(cellValue) And cellValue <> "" Then
            recordId = idPrefix & "-" & Format(Now, "yyyymmddhhnnss") & "-" & Right("0000" & Int(Rnd() * 10000), 4)
            sql = "INSERT INTO " & tableName & " (id, inn_code, row_index, ratio_value) VALUES ('" & _
                  recordId & "', '" & DataHelper.EscapeSQL(innCode) & "', " & row & ", " & cellValue & _
                  ") ON DUPLICATE KEY UPDATE ratio_value = " & cellValue & ", updated_at = NOW()"
        Else
            sql = "DELETE FROM " & tableName & " WHERE inn_code = '" & DataHelper.EscapeSQL(innCode) & "' AND row_index = " & row
        End If

        If DabaseCore.ExecuteCommandSilent(sql) Then successCount = successCount + 1
    Next row

    SaveRatioData = successCount
    Exit Function

ErrorHandler:
    SaveRatioData = 0
End Function

' 通用占比数据加载函数
Private Function LoadRatioData(ws As Worksheet, innCode As String, startRow As Integer, endRow As Integer, dataCol As Integer, tableName As String) As Integer
    On Error GoTo ErrorHandler

    LoadRatioData = 0
    Dim sql As String, rs As Object, loadedCount As Integer

    sql = "SELECT row_index, ratio_value FROM " & tableName & " WHERE inn_code = '" & _
          DataHelper.EscapeSQL(innCode) & "' ORDER BY row_index"
    Set rs = DabaseCore.ExecuteQuerySilent(sql)

    If Not rs Is Nothing Then
        Do While Not rs.EOF
            Dim rowIndex As Integer, ratioValue As Double
            rowIndex = CInt(rs.Fields("row_index").Value)
            ratioValue = CDbl(rs.Fields("ratio_value").Value)

            If rowIndex >= startRow And rowIndex <= endRow Then
                ws.Cells(rowIndex, dataCol).Value = ratioValue
                loadedCount = loadedCount + 1
            End If
            rs.MoveNext
        Loop
        rs.Close: Set rs = Nothing
    End If

    LoadRatioData = loadedCount
    Exit Function

ErrorHandler:
    If Not rs Is Nothing Then
        If rs.State = 1 Then rs.Close: Set rs = Nothing
    End If
    LoadRatioData = 0
End Function